"""
Quick fix for the peak detection issue in the notebook.
Add this code cell before the visualization sections.
"""

# Fix for peak detection arrays - ensure they are always numpy arrays
import numpy as np

# Ensure all peak arrays are proper numpy arrays
if not isinstance(hr_peaks, np.ndarray):
    hr_peaks = np.array([hr_peaks]) if isinstance(hr_peaks, (int, float)) else np.array([])

if not isinstance(wav_hr_peaks, np.ndarray):
    wav_hr_peaks = np.array([wav_hr_peaks]) if isinstance(wav_hr_peaks, (int, float)) else np.array([])

if not isinstance(resp_peaks, np.ndarray):
    resp_peaks = np.array([resp_peaks]) if isinstance(resp_peaks, (int, float)) else np.array([])

if not isinstance(emd_hr_peaks, np.ndarray):
    emd_hr_peaks = np.array([emd_hr_peaks]) if isinstance(emd_hr_peaks, (int, float)) else np.array([])

if not isinstance(emd_resp_peaks, np.ndarray):
    emd_resp_peaks = np.array([emd_resp_peaks]) if isinstance(emd_resp_peaks, (int, float)) else np.array([])

print("✓ Peak detection arrays fixed and validated")
print(f"  - hr_peaks: {type(hr_peaks)}, length: {len(hr_peaks)}")
print(f"  - wav_hr_peaks: {type(wav_hr_peaks)}, length: {len(wav_hr_peaks)}")
print(f"  - resp_peaks: {type(resp_peaks)}, length: {len(resp_peaks)}")
print(f"  - emd_hr_peaks: {type(emd_hr_peaks)}, length: {len(emd_hr_peaks)}")
print(f"  - emd_resp_peaks: {type(emd_resp_peaks)}, length: {len(emd_resp_peaks)}")
