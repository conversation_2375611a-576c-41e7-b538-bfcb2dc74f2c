{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PPG Signal Analysis: Fourier Transform, Wavelet Transform, and EMD\n", "\n", "This notebook demonstrates comprehensive PPG signal analysis using three different approaches:\n", "1. **Fourier Transform** - Bandpass filtering for heart rate extraction\n", "2. **Wavelet Transform** - Noise reduction and respiration rate detection\n", "3. **Empirical Mode Decomposition (EMD)** - Signal decomposition for both heart rate and respiration rate\n", "\n", "## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from hw2_utils import *\n", "\n", "# Set up plotting parameters\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(\"All imports successful!\")\n", "print(\"Available functions:\")\n", "print(\"- load_ppg_data_sample()\")\n", "print(\"- FFT-based analysis functions\")\n", "print(\"- Wavelet-based analysis functions\")\n", "print(\"- EMD-based analysis functions\")\n", "print(\"- comprehensive_ppg_analysis()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load PPG Data\n", "\n", "For this demonstration, we'll use synthetic PPG data that mimics real PPG signals with:\n", "- Heart rate component (~72 bpm)\n", "- Respiratory component (~15 breaths/min)\n", "- Various noise sources (motion artifacts, high-frequency noise)\n", "\n", "**Note**: In practice, replace `load_ppg_data_sample()` with actual PPG DaLia dataset loading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load synthetic PPG data\n", "duration = 60  # seconds\n", "sampling_freq = 100  # Hz\n", "\n", "ppg_signal, fs = load_ppg_data_sample(duration=duration, sampling_freq=sampling_freq)\n", "time_axis = np.arange(len(ppg_signal)) / fs\n", "\n", "print(f\"Loaded PPG signal:\")\n", "print(f\"- Duration: {duration} seconds\")\n", "print(f\"- Sampling frequency: {fs} Hz\")\n", "print(f\"- Number of samples: {len(ppg_signal)}\")\n", "print(f\"- Signal range: {np.min(ppg_signal):.3f} to {np.max(ppg_signal):.3f}\")\n", "\n", "# Plot original signal\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(time_axis, ppg_signal, 'b-', alpha=0.8, linewidth=1)\n", "plt.title('Original PPG Signal (Synthetic Data)', fontsize=14)\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Amplitude')\n", "plt.grid(True, alpha=0.3)\n", "plt.xlim(0, min(20, duration))  # Show first 20 seconds\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Task 1: Fourier Transform Analysis\n", "\n", "### Objectives:\n", "- Apply Fourier Transform to visualize frequency spectrum\n", "- Design bandpass filter (0.7-4 Hz) for heart rate extraction\n", "- Apply inverse FFT to get filtered signal\n", "- Detect peaks to determine heart rate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== TASK 1: FOURIER TRANSFORM ANALYSIS ===\")\n", "print()\n", "\n", "# Step 1: Compute FFT spectrum of original signal\n", "frequencies, magnitude, complex_spectrum = compute_fft_spectrum(ppg_signal, fs)\n", "\n", "# Step 2: Apply bandpass filter (0.7-4 Hz for heart rate)\n", "heart_rate_filtered = apply_fft_bandpass_filter(ppg_signal, 0.7, 4.0, fs)\n", "\n", "# Step 3: Detect heart rate from filtered signal\n", "hr_peaks, mean_hr, instantaneous_hr = detect_heart_rate_from_peaks(heart_rate_filtered, fs)\n", "\n", "print(f\"FFT Analysis Results:\")\n", "print(f\"- Detected {len(hr_peaks)} heart rate peaks\")\n", "print(f\"- Mean heart rate: {mean_hr:.1f} bpm\")\n", "print(f\"- Heart rate variability: {np.std(instantaneous_hr):.1f} bpm\")\n", "print()\n", "\n", "# Plot FFT analysis results\n", "plot_fft_analysis(ppg_signal, heart_rate_filtered, fs, \"FFT-based Heart Rate Extraction\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot heart rate peaks on filtered signal\n", "plt.figure(figsize=(15, 6))\n", "time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds\n", "signal_segment = heart_rate_filtered[:len(time_segment)]\n", "peaks_in_segment = hr_peaks[hr_peaks < len(time_segment)]\n", "\n", "plt.plot(time_segment, signal_segment, 'b-', alpha=0.8, label='Filtered PPG')\n", "if len(peaks_in_segment) > 0:\n", "    plt.plot(time_segment[peaks_in_segment], signal_segment[peaks_in_segment], \n", "             'ro', markersize=8, label=f'Heart Rate Peaks ({len(hr_peaks)} total)')\n", "\n", "plt.title('FFT-Filtered PPG Signal with Heart Rate Peaks', fontsize=14)\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Heart rate statistics:\")\n", "if len(instantaneous_hr) > 0:\n", "    print(f\"- Min HR: {np.min(instantaneous_hr):.1f} bpm\")\n", "    print(f\"- Max HR: {np.max(instantaneous_hr):.1f} bpm\")\n", "    print(f\"- Std HR: {np.std(instantaneous_hr):.1f} bpm\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Task 2: Wavelet Transform Analysis\n", "\n", "### Objectives:\n", "- Apply discrete wavelet transform (DWT) using suitable wavelet (bior3.9)\n", "- Use thresholding for noise reduction\n", "- Reconstruct signal from modified coefficients\n", "- Detect peaks for both heart rate and respiration rate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== TASK 2: WAVELET TRANSFORM ANALYSIS ===\")\n", "print()\n", "\n", "# Step 1: Apply wavelet denoising\n", "wavelet_type = 'bior3.9'\n", "decomposition_levels = 5\n", "\n", "denoised_signal, coeffs_orig, coeffs_thresh = wavelet_denoise_ppg(\n", "    ppg_signal, \n", "    wavelet=wavelet_type, \n", "    levels=decomposition_levels,\n", "    threshold_mode='soft',\n", "    threshold_method='sure'\n", ")\n", "\n", "print(f\"Wavelet Analysis Parameters:\")\n", "print(f\"- Wavelet type: {wavelet_type}\")\n", "print(f\"- Decomposition levels: {decomposition_levels}\")\n", "print(f\"- Thresholding: soft, SURE method\")\n", "print()\n", "\n", "# Step 2: Detect heart rate from denoised signal\n", "wav_hr_peaks, wav_mean_hr, wav_instantaneous_hr = detect_heart_rate_from_peaks(denoised_signal, fs)\n", "\n", "# Step 3: Detect respiration rate\n", "wav_resp_peaks, wav_mean_rr, wav_instantaneous_rr = detect_respiration_from_ppg(denoised_signal, fs)\n", "\n", "print(f\"Wavelet Analysis Results:\")\n", "print(f\"- Heart rate peaks detected: {len(wav_hr_peaks)}\")\n", "print(f\"- Mean heart rate: {wav_mean_hr:.1f} bpm\")\n", "print(f\"- Respiration peaks detected: {len(wav_resp_peaks)}\")\n", "print(f\"- Mean respiration rate: {wav_mean_rr:.1f} breaths/min\")\n", "print()\n", "\n", "# Plot wavelet analysis\n", "plot_wavelet_analysis(ppg_signal, denoised_signal, coeffs_orig, coeffs_thresh, fs, wavelet_type)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot denoised signal with detected peaks\n", "plt.figure(figsize=(15, 8))\n", "\n", "# Create subplots\n", "plt.subplot(2, 1, 1)\n", "time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds\n", "signal_segment = denoised_signal[:len(time_segment)]\n", "hr_peaks_segment = wav_hr_peaks[wav_hr_peaks < len(time_segment)]\n", "\n", "plt.plot(time_segment, signal_segment, 'g-', alpha=0.8, label='Wavelet Denoised PPG')\n", "if len(hr_peaks_segment) > 0:\n", "    plt.plot(time_segment[hr_peaks_segment], signal_segment[hr_peaks_segment], \n", "             'ro', markersize=8, label=f'Heart Rate Peaks ({len(wav_hr_peaks)} total)')\n", "\n", "plt.title('Wavelet Denoised PPG Signal with Heart Rate Detection', fontsize=14)\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Respiration component (if detected)\n", "plt.subplot(2, 1, 2)\n", "if len(wav_resp_peaks) > 0:\n", "    resp_peaks_segment = wav_resp_peaks[wav_resp_peaks < len(time_segment)]\n", "    plt.plot(time_segment, signal_segment, 'b-', alpha=0.5, label='PPG Signal')\n", "    if len(resp_peaks_segment) > 0:\n", "        plt.plot(time_segment[resp_peaks_segment], signal_segment[resp_peaks_segment], \n", "                 'mo', markersize=10, label=f'Respiration Peaks ({len(wav_resp_peaks)} total)')\n", "    plt.title('Respiration Rate Detection from PPG Envelope', fontsize=14)\n", "else:\n", "    plt.plot(time_segment, signal_segment, 'b-', alpha=0.8)\n", "    plt.title('No Respiration Peaks Detected', fontsize=14)\n", "\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Task 3: Empirical Mode Decomposition (EMD)\n", "\n", "### Objectives:\n", "- Decompose PPG signal into Intrinsic Mode Functions (IMFs)\n", "- Identify and remove noise IMFs\n", "- Reconstruct signal from relevant IMFs\n", "- Extract both heart rate and respiration rate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== TASK 3: EMPIRICAL MODE DECOMPOSITION (EMD) ===\")\n", "print()\n", "\n", "# Step 1: Perform EMD decomposition\n", "print(\"Performing EMD decomposition...\")\n", "emd_residue, emd_imfs = emd_decompose_ppg(ppg_signal, method='EMD')\n", "\n", "print(f\"EMD Decomposition Results:\")\n", "print(f\"- Number of IMFs: {len(emd_imfs)}\")\n", "print(f\"- Residue shape: {emd_residue.shape}\")\n", "print()\n", "\n", "# Step 2: Analyze IMF frequency content and select relevant ones\n", "hr_imf_indices, rr_imf_indices, noise_imf_indices = select_relevant_imfs(\n", "    emd_imfs, fs, \n", "    heart_rate_range=(0.7, 4.0),\n", "    resp_rate_range=(0.1, 0.5)\n", ")\n", "\n", "print(f\"IMF Classification:\")\n", "print(f\"- Heart rate IMFs: {hr_imf_indices}\")\n", "print(f\"- Respiration rate IMFs: {rr_imf_indices}\")\n", "print(f\"- Noise IMFs: {noise_imf_indices}\")\n", "print()\n", "\n", "# Step 3: Reconstruct signals from selected IMFs\n", "emd_hr_signal = reconstruct_from_selected_imfs(emd_imfs, hr_imf_indices)\n", "emd_rr_signal = reconstruct_from_selected_imfs(emd_imfs, rr_imf_indices)\n", "emd_clean_signal = reconstruct_from_selected_imfs(emd_imfs, hr_imf_indices + rr_imf_indices, emd_residue)\n", "\n", "print(f\"Signal Reconstruction:\")\n", "print(f\"- Heart rate signal length: {len(emd_hr_signal)}\")\n", "print(f\"- Respiration signal length: {len(emd_rr_signal)}\")\n", "print(f\"- Clean signal length: {len(emd_clean_signal)}\")\n", "print()\n", "\n", "# Plot EMD decomposition\n", "plot_emd_analysis(ppg_signal, emd_imfs, emd_residue, fs, max_imfs_to_plot=6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 4: Detect peaks in reconstructed signals\n", "emd_hr_peaks, emd_mean_hr, emd_instantaneous_hr = [], 0, []\n", "emd_rr_peaks, emd_mean_rr, emd_instantaneous_rr = [], 0, []\n", "\n", "if len(emd_hr_signal) > 0 and np.any(emd_hr_signal != 0):\n", "    emd_hr_peaks, emd_mean_hr, emd_instantaneous_hr = detect_heart_rate_from_peaks(emd_hr_signal, fs)\n", "\n", "if len(emd_rr_signal) > 0 and np.any(emd_rr_signal != 0):\n", "    emd_rr_peaks, emd_mean_rr, emd_instantaneous_rr = detect_respiration_from_ppg(emd_rr_signal, fs)\n", "\n", "print(f\"EMD Peak Detection Results:\")\n", "print(f\"- Heart rate peaks: {len(emd_hr_peaks)}\")\n", "print(f\"- Mean heart rate: {emd_mean_hr:.1f} bpm\")\n", "print(f\"- Respiration peaks: {len(emd_rr_peaks)}\")\n", "print(f\"- Mean respiration rate: {emd_mean_rr:.1f} breaths/min\")\n", "print()\n", "\n", "# Plot reconstructed signals with peaks\n", "fig, axes = plt.subplots(3, 1, figsize=(15, 12))\n", "time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds\n", "\n", "# Original vs Clean signal\n", "axes[0].plot(time_segment, ppg_signal[:len(time_segment)], 'b-', alpha=0.6, label='Original PPG')\n", "axes[0].plot(time_segment, emd_clean_signal[:len(time_segment)], 'r-', alpha=0.8, label='EMD Reconstructed')\n", "axes[0].set_title('Original vs EMD Reconstructed Signal', fontsize=14)\n", "axes[0].set_ylabel('Amplitude')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Heart rate component\n", "if len(emd_hr_signal) > 0:\n", "    hr_segment = emd_hr_signal[:len(time_segment)]\n", "    hr_peaks_segment = emd_hr_peaks[emd_hr_peaks < len(time_segment)]\n", "    \n", "    axes[1].plot(time_segment, hr_segment, 'g-', alpha=0.8, label='Heart Rate Component')\n", "    if len(hr_peaks_segment) > 0:\n", "        axes[1].plot(time_segment[hr_peaks_segment], hr_segment[hr_peaks_segment], \n", "                     'ro', markersize=8, label=f'HR Peaks ({len(emd_hr_peaks)} total)')\n", "    axes[1].set_title('EMD Heart Rate Component', fontsize=14)\n", "else:\n", "    axes[1].plot(time_segment, np.zeros_like(time_segment), 'g-')\n", "    axes[1].set_title('No Heart Rate Component Detected', fontsize=14)\n", "\n", "axes[1].set_ylabel('Amplitude')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# Respiration component\n", "if len(emd_rr_signal) > 0:\n", "    rr_segment = emd_rr_signal[:len(time_segment)]\n", "    rr_peaks_segment = emd_rr_peaks[emd_rr_peaks < len(time_segment)]\n", "    \n", "    axes[2].plot(time_segment, rr_segment, 'm-', alpha=0.8, label='Respiration Component')\n", "    if len(rr_peaks_segment) > 0:\n", "        axes[2].plot(time_segment[rr_peaks_segment], rr_segment[rr_peaks_segment], \n", "                     'bo', markersize=10, label=f'RR Peaks ({len(emd_rr_peaks)} total)')\n", "    axes[2].set_title('EMD Respiration Component', fontsize=14)\n", "else:\n", "    axes[2].plot(time_segment, np.zeros_like(time_segment), 'm-')\n", "    axes[2].set_title('No Respiration Component Detected', fontsize=14)\n", "\n", "axes[2].set_xlabel('Time (seconds)')\n", "axes[2].set_ylabel('Amplitude')\n", "axes[2].legend()\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Comprehensive Comparison of All Methods\n", "\n", "Let's compare the results from all three methods and analyze their performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== COMPREHENSIVE ANALYSIS COMPARISON ===\")\n", "print()\n", "\n", "# Run comprehensive analysis\n", "all_results = comprehensive_ppg_analysis(ppg_signal, fs, methods=['FFT', 'Wavelet', 'EMD'])\n", "\n", "# Extract results for comparison\n", "methods_comparison = {\n", "    'FFT': {\n", "        'heart_rate': all_results['fft']['heart_rate'],\n", "        'num_peaks': len(all_results['fft']['peaks']),\n", "        'hr_std': np.std(all_results['fft']['instantaneous_hr']) if all_results['fft']['instantaneous_hr'] else 0\n", "    },\n", "    'Wavelet': {\n", "        'heart_rate': all_results['wavelet']['heart_rate'],\n", "        'num_peaks': len(all_results['wavelet']['heart_peaks']),\n", "        'hr_std': np.std(all_results['wavelet']['instantaneous_hr']) if all_results['wavelet']['instantaneous_hr'] else 0,\n", "        'resp_rate': all_results['wavelet']['resp_rate'],\n", "        'num_resp_peaks': len(all_results['wavelet']['resp_peaks'])\n", "    },\n", "    'EMD': {\n", "        'heart_rate': all_results['emd']['heart_rate'],\n", "        'num_peaks': len(all_results['emd']['heart_peaks']),\n", "        'hr_std': np.std(all_results['emd']['instantaneous_hr']) if all_results['emd']['instantaneous_hr'] else 0,\n", "        'resp_rate': all_results['emd']['resp_rate'],\n", "        'num_resp_peaks': len(all_results['emd']['resp_peaks']),\n", "        'num_imfs': len(all_results['emd']['imfs'])\n", "    }\n", "}\n", "\n", "# Create comparison table\n", "print(\"Method Comparison Results:\")\n", "print(\"=\" * 80)\n", "print(f\"{'Method':<10} {'HR (bpm)':<10} {'HR Peaks':<10} {'HR Std':<10} {'RR (br/min)':<12} {'RR Peaks':<10}\")\n", "print(\"-\" * 80)\n", "\n", "for method, results in methods_comparison.items():\n", "    hr = results['heart_rate']\n", "    hr_peaks = results['num_peaks']\n", "    hr_std = results['hr_std']\n", "    rr = results.get('resp_rate', 'N/A')\n", "    rr_peaks = results.get('num_resp_peaks', 'N/A')\n", "    \n", "    rr_str = f\"{rr:.1f}\" if isinstance(rr, (int, float)) else str(rr)\n", "    rr_peaks_str = str(rr_peaks) if isinstance(rr_peaks, (int, float)) else str(rr_peaks)\n", "    \n", "    print(f\"{method:<10} {hr:<10.1f} {hr_peaks:<10} {hr_std:<10.1f} {rr_str:<12} {rr_peaks_str:<10}\")\n", "\n", "print(\"\\nAdditional EMD Information:\")\n", "print(f\"- Number of IMFs: {methods_comparison['EMD']['num_imfs']}\")\n", "print(f\"- Heart rate IMFs: {all_results['emd']['heart_rate_imfs']}\")\n", "print(f\"- Respiration IMFs: {all_results['emd']['resp_rate_imfs']}\")\n", "print(f\"- Noise IMFs: {all_results['emd']['noise_imfs']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot comparison of all filtered/processed signals\n", "plt.figure(figsize=(15, 12))\n", "\n", "time_segment = time_axis[:min(1500, len(time_axis))]  # First 15 seconds for clarity\n", "\n", "# Original signal\n", "plt.subplot(4, 1, 1)\n", "plt.plot(time_segment, ppg_signal[:len(time_segment)], 'k-', alpha=0.8, linewidth=1)\n", "plt.title('Original PPG Signal', fontsize=14)\n", "plt.ylabel('Amplitude')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# FFT filtered\n", "plt.subplot(4, 1, 2)\n", "fft_segment = all_results['fft']['filtered_signal'][:len(time_segment)]\n", "fft_peaks_segment = all_results['fft']['peaks'][all_results['fft']['peaks'] < len(time_segment)]\n", "plt.plot(time_segment, fft_segment, 'b-', alpha=0.8, linewidth=1, label='FFT Filtered')\n", "if len(fft_peaks_segment) > 0:\n", "    plt.plot(time_segment[fft_peaks_segment], fft_segment[fft_peaks_segment], \n", "             'ro', markersize=6, label=f'HR: {all_results[\"fft\"][\"heart_rate\"]:.1f} bpm')\n", "plt.title('FFT Bandpass Filtered (0.7-4 Hz)', fontsize=14)\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# <PERSON><PERSON> denoised\n", "plt.subplot(4, 1, 3)\n", "wav_segment = all_results['wavelet']['denoised_signal'][:len(time_segment)]\n", "wav_peaks_segment = all_results['wavelet']['heart_peaks'][all_results['wavelet']['heart_peaks'] < len(time_segment)]\n", "plt.plot(time_segment, wav_segment, 'g-', alpha=0.8, linewidth=1, label='Wavelet Denoised')\n", "if len(wav_peaks_segment) > 0:\n", "    plt.plot(time_segment[wav_peaks_segment], wav_segment[wav_peaks_segment], \n", "             'ro', markersize=6, label=f'HR: {all_results[\"wavelet\"][\"heart_rate\"]:.1f} bpm')\n", "plt.title('<PERSON><PERSON> Denoised (bior3.9, 5 levels)', fontsize=14)\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# EMD reconstructed\n", "plt.subplot(4, 1, 4)\n", "if len(all_results['emd']['heart_rate_signal']) > 0:\n", "    emd_segment = all_results['emd']['heart_rate_signal'][:len(time_segment)]\n", "    emd_peaks_segment = all_results['emd']['heart_peaks'][all_results['emd']['heart_peaks'] < len(time_segment)]\n", "    plt.plot(time_segment, emd_segment, 'm-', alpha=0.8, linewidth=1, label='EMD Reconstructed')\n", "    if len(emd_peaks_segment) > 0:\n", "        plt.plot(time_segment[emd_peaks_segment], emd_segment[emd_peaks_segment], \n", "                 'ro', markersize=6, label=f'HR: {all_results[\"emd\"][\"heart_rate\"]:.1f} bpm')\n", "else:\n", "    plt.plot(time_segment, np.zeros_like(time_segment), 'm-', alpha=0.8, linewidth=1, label='No EMD HR Signal')\n", "\n", "plt.title('EMD Heart Rate Component', fontsize=14)\n", "plt.xlabel('Time (seconds)')\n", "plt.ylabel('Amplitude')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Summary and Conclusions\n", "\n", "### Method Performance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== SUMMARY AND CONCLUSIONS ===\")\n", "print()\n", "\n", "print(\"1. FOURIER TRANSFORM (FFT) METHOD:\")\n", "print(\"   Strengths:\")\n", "print(\"   - Simple and computationally efficient\")\n", "print(\"   - Good for isolating specific frequency bands\")\n", "print(\"   - Clear frequency domain visualization\")\n", "print(\"   Limitations:\")\n", "print(\"   - Assumes stationary signals\")\n", "print(\"   - May not handle non-linear components well\")\n", "print(\"   - Fixed frequency bands may miss adaptive features\")\n", "print()\n", "\n", "print(\"2. WAVELET TRANSFORM METHOD:\")\n", "print(\"   Strengths:\")\n", "print(\"   - Excellent for noise reduction\")\n", "print(\"   - Time-frequency localization\")\n", "print(\"   - Adaptive thresholding\")\n", "print(\"   - Can detect both heart rate and respiration\")\n", "print(\"   Limitations:\")\n", "print(\"   - Wavelet selection affects performance\")\n", "print(\"   - Parameter tuning required\")\n", "print(\"   - May over-smooth important features\")\n", "print()\n", "\n", "print(\"3. EMPIRICAL MODE DECOMPOSITION (EMD):\")\n", "print(\"   Strengths:\")\n", "print(\"   - Data-adaptive decomposition\")\n", "print(\"   - No predefined basis functions\")\n", "print(\"   - Can separate multiple physiological components\")\n", "print(\"   - Handles non-stationary and non-linear signals\")\n", "print(\"   Limitations:\")\n", "print(\"   - Computationally intensive\")\n", "print(\"   - Mode mixing issues\")\n", "print(\"   - Sensitive to noise\")\n", "print(\"   - IMF selection requires domain knowledge\")\n", "print()\n", "\n", "# Performance metrics\n", "print(\"PERFORMANCE COMPARISON:\")\n", "print(f\"Expected heart rate: ~72 bpm (1.2 Hz)\")\n", "print(f\"Expected respiration rate: ~15 breaths/min (0.25 Hz)\")\n", "print()\n", "\n", "for method, results in methods_comparison.items():\n", "    hr_error = abs(results['heart_rate'] - 72) if results['heart_rate'] > 0 else float('inf')\n", "    print(f\"{method} Method:\")\n", "    print(f\"  - Heart rate accuracy: {hr_error:.1f} bpm error\")\n", "    print(f\"  - Heart rate variability: {results['hr_std']:.1f} bpm\")\n", "    if 'resp_rate' in results and isinstance(results['resp_rate'], (int, float)):\n", "        rr_error = abs(results['resp_rate'] - 15)\n", "        print(f\"  - Respiration rate accuracy: {rr_error:.1f} breaths/min error\")\n", "    print()\n", "\n", "print(\"RECOMMENDATIONS:\")\n", "print(\"- Use FFT for quick heart rate estimation with clean signals\")\n", "print(\"- Use Wavelet for robust denoising and dual HR/RR detection\")\n", "print(\"- Use EMD for complex signals with multiple physiological components\")\n", "print(\"- Consider ensemble methods (EEMD/CEEMDAN) for noisy signals\")\n", "print(\"- Combine methods for robust multi-parameter estimation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Next Steps and Extensions\n", "\n", "### Potential Improvements:\n", "1. **Real Data Integration**: Replace synthetic data with actual PPG DaLia dataset\n", "2. **Advanced EMD**: Implement EEMD or CEEMDAN for better noise robustness\n", "3. **Machine Learning**: Add ML-based peak detection and signal quality assessment\n", "4. **Real-time Processing**: Optimize algorithms for real-time applications\n", "5. **Multi-modal Analysis**: Combine with other physiological signals (ECG, accelerometer)\n", "\n", "### Code Usage:\n", "```python\n", "# Quick analysis with all methods\n", "results = comprehensive_ppg_analysis(your_ppg_signal, sampling_freq)\n", "\n", "# Individual method usage\n", "fft_filtered = apply_fft_bandpass_filter(signal, 0.7, 4.0, fs)\n", "denoised, _, _ = wavelet_denoise_ppg(signal, wavelet='bior3.9')\n", "residue, imfs = emd_decompose_ppg(signal, method='EMD')\n", "```\n", "\n", "All functions are available in `hw2_utils.py` for integration into your own projects!"]}], "metadata": {"kernelspec": {"display_name": "tf_gpu", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}