import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from hw2_utils import *

# Set up plotting parameters
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

print("All imports successful!")
print("Available functions:")
print("- load_ppg_data_sample()")
print("- FFT-based analysis functions")
print("- Wavelet-based analysis functions")
print("- EMD-based analysis functions")
print("- comprehensive_ppg_analysis()")

# Load synthetic PPG data
duration = 60  # seconds
sampling_freq = 100  # Hz

ppg_signal, fs = load_ppg_data_sample(duration=duration, sampling_freq=sampling_freq)
time_axis = np.arange(len(ppg_signal)) / fs

print(f"Loaded PPG signal:")
print(f"- Duration: {duration} seconds")
print(f"- Sampling frequency: {fs} Hz")
print(f"- Number of samples: {len(ppg_signal)}")
print(f"- Signal range: {np.min(ppg_signal):.3f} to {np.max(ppg_signal):.3f}")

# Plot original signal
plt.figure(figsize=(15, 6))
plt.plot(time_axis, ppg_signal, 'b-', alpha=0.8, linewidth=1)
plt.title('Original PPG Signal (Synthetic Data)', fontsize=14)
plt.xlabel('Time (seconds)')
plt.ylabel('Amplitude')
plt.grid(True, alpha=0.3)
plt.xlim(0, min(20, duration))  # Show first 20 seconds
plt.tight_layout()
plt.show()

print("=== TASK 1: FOURIER TRANSFORM ANALYSIS ===")
print()

# Step 1: Compute FFT spectrum of original signal
frequencies, magnitude, complex_spectrum = compute_fft_spectrum(ppg_signal, fs)

# Step 2: Apply bandpass filter (0.7-4 Hz for heart rate)
heart_rate_filtered = apply_fft_bandpass_filter(ppg_signal, 0.7, 4.0, fs)

# Step 3: Detect heart rate from filtered signal
hr_peaks, mean_hr, instantaneous_hr = detect_heart_rate_from_peaks(heart_rate_filtered, fs)

print(f"FFT Analysis Results:")
print(f"- Detected {len(hr_peaks)} heart rate peaks")
print(f"- Mean heart rate: {mean_hr:.1f} bpm")
print(f"- Heart rate variability: {np.std(instantaneous_hr):.1f} bpm")
print()

# Plot FFT analysis results
plot_fft_analysis(ppg_signal, heart_rate_filtered, fs, "FFT-based Heart Rate Extraction")

# Plot heart rate peaks on filtered signal
plt.figure(figsize=(15, 6))
time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds
signal_segment = heart_rate_filtered[:len(time_segment)]
peaks_in_segment = hr_peaks[hr_peaks < len(time_segment)]

plt.plot(time_segment, signal_segment, 'b-', alpha=0.8, label='Filtered PPG')
if len(peaks_in_segment) > 0:
    plt.plot(time_segment[peaks_in_segment], signal_segment[peaks_in_segment], 
             'ro', markersize=8, label=f'Heart Rate Peaks ({len(hr_peaks)} total)')

plt.title('FFT-Filtered PPG Signal with Heart Rate Peaks', fontsize=14)
plt.xlabel('Time (seconds)')
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print(f"Heart rate statistics:")
if len(instantaneous_hr) > 0:
    print(f"- Min HR: {np.min(instantaneous_hr):.1f} bpm")
    print(f"- Max HR: {np.max(instantaneous_hr):.1f} bpm")
    print(f"- Std HR: {np.std(instantaneous_hr):.1f} bpm")

print("=== TASK 2: WAVELET TRANSFORM ANALYSIS ===")
print()

# Step 1: Apply wavelet denoising
wavelet_type = 'bior3.9'
decomposition_levels = 5

denoised_signal, coeffs_orig, coeffs_thresh = wavelet_denoise_ppg(
    ppg_signal, 
    wavelet=wavelet_type, 
    levels=decomposition_levels,
    threshold_mode='soft',
    threshold_method='sure'
)

print(f"Wavelet Analysis Parameters:")
print(f"- Wavelet type: {wavelet_type}")
print(f"- Decomposition levels: {decomposition_levels}")
print(f"- Thresholding: soft, SURE method")
print()

# Step 2: Detect heart rate from denoised signal
wav_hr_peaks, wav_mean_hr, wav_instantaneous_hr = detect_heart_rate_from_peaks(denoised_signal, fs)

# Step 3: Detect respiration rate
wav_resp_peaks, wav_mean_rr, wav_instantaneous_rr = detect_respiration_from_ppg(denoised_signal, fs)

print(f"Wavelet Analysis Results:")
print(f"- Heart rate peaks detected: {len(wav_hr_peaks)}")
print(f"- Mean heart rate: {wav_mean_hr:.1f} bpm")
print(f"- Respiration peaks detected: {len(wav_resp_peaks)}")
print(f"- Mean respiration rate: {wav_mean_rr:.1f} breaths/min")
print()

# Plot wavelet analysis
plot_wavelet_analysis(ppg_signal, denoised_signal, coeffs_orig, coeffs_thresh, fs, wavelet_type)

# Plot denoised signal with detected peaks
plt.figure(figsize=(15, 8))

# Create subplots
plt.subplot(2, 1, 1)
time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds
signal_segment = denoised_signal[:len(time_segment)]
hr_peaks_segment = wav_hr_peaks[wav_hr_peaks < len(time_segment)]

plt.plot(time_segment, signal_segment, 'g-', alpha=0.8, label='Wavelet Denoised PPG')
if len(hr_peaks_segment) > 0:
    plt.plot(time_segment[hr_peaks_segment], signal_segment[hr_peaks_segment], 
             'ro', markersize=8, label=f'Heart Rate Peaks ({len(wav_hr_peaks)} total)')

plt.title('Wavelet Denoised PPG Signal with Heart Rate Detection', fontsize=14)
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)

# Respiration component (if detected)
plt.subplot(2, 1, 2)
if len(wav_resp_peaks) > 0:
    resp_peaks_segment = wav_resp_peaks[wav_resp_peaks < len(time_segment)]
    plt.plot(time_segment, signal_segment, 'b-', alpha=0.5, label='PPG Signal')
    if len(resp_peaks_segment) > 0:
        plt.plot(time_segment[resp_peaks_segment], signal_segment[resp_peaks_segment], 
                 'mo', markersize=10, label=f'Respiration Peaks ({len(wav_resp_peaks)} total)')
    plt.title('Respiration Rate Detection from PPG Envelope', fontsize=14)
else:
    plt.plot(time_segment, signal_segment, 'b-', alpha=0.8)
    plt.title('No Respiration Peaks Detected', fontsize=14)

plt.xlabel('Time (seconds)')
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print("=== TASK 3: EMPIRICAL MODE DECOMPOSITION (EMD) ===")
print()

# Step 1: Perform EMD decomposition
print("Performing EMD decomposition...")
emd_residue, emd_imfs = emd_decompose_ppg(ppg_signal, method='EMD')

print(f"EMD Decomposition Results:")
print(f"- Number of IMFs: {len(emd_imfs)}")
print(f"- Residue shape: {emd_residue.shape}")
print()

# Step 2: Analyze IMF frequency content and select relevant ones
hr_imf_indices, rr_imf_indices, noise_imf_indices = select_relevant_imfs(
    emd_imfs, fs, 
    heart_rate_range=(0.7, 4.0),
    resp_rate_range=(0.1, 0.5)
)

print(f"IMF Classification:")
print(f"- Heart rate IMFs: {hr_imf_indices}")
print(f"- Respiration rate IMFs: {rr_imf_indices}")
print(f"- Noise IMFs: {noise_imf_indices}")
print()

# Step 3: Reconstruct signals from selected IMFs
emd_hr_signal = reconstruct_from_selected_imfs(emd_imfs, hr_imf_indices)
emd_rr_signal = reconstruct_from_selected_imfs(emd_imfs, rr_imf_indices)
emd_clean_signal = reconstruct_from_selected_imfs(emd_imfs, hr_imf_indices + rr_imf_indices, emd_residue)

print(f"Signal Reconstruction:")
print(f"- Heart rate signal length: {len(emd_hr_signal)}")
print(f"- Respiration signal length: {len(emd_rr_signal)}")
print(f"- Clean signal length: {len(emd_clean_signal)}")
print()

# Plot EMD decomposition
plot_emd_analysis(ppg_signal, emd_imfs, emd_residue, fs, max_imfs_to_plot=6)

# Step 4: Detect peaks in reconstructed signals
emd_hr_peaks, emd_mean_hr, emd_instantaneous_hr = [], 0, []
emd_rr_peaks, emd_mean_rr, emd_instantaneous_rr = [], 0, []

if len(emd_hr_signal) > 0 and np.any(emd_hr_signal != 0):
    emd_hr_peaks, emd_mean_hr, emd_instantaneous_hr = detect_heart_rate_from_peaks(emd_hr_signal, fs)

if len(emd_rr_signal) > 0 and np.any(emd_rr_signal != 0):
    emd_rr_peaks, emd_mean_rr, emd_instantaneous_rr = detect_respiration_from_ppg(emd_rr_signal, fs)

print(f"EMD Peak Detection Results:")
print(f"- Heart rate peaks: {len(emd_hr_peaks)}")
print(f"- Mean heart rate: {emd_mean_hr:.1f} bpm")
print(f"- Respiration peaks: {len(emd_rr_peaks)}")
print(f"- Mean respiration rate: {emd_mean_rr:.1f} breaths/min")
print()

# Plot reconstructed signals with peaks
fig, axes = plt.subplots(3, 1, figsize=(15, 12))
time_segment = time_axis[:min(2000, len(time_axis))]  # First 20 seconds

# Original vs Clean signal
axes[0].plot(time_segment, ppg_signal[:len(time_segment)], 'b-', alpha=0.6, label='Original PPG')
axes[0].plot(time_segment, emd_clean_signal[:len(time_segment)], 'r-', alpha=0.8, label='EMD Reconstructed')
axes[0].set_title('Original vs EMD Reconstructed Signal', fontsize=14)
axes[0].set_ylabel('Amplitude')
axes[0].legend()
axes[0].grid(True, alpha=0.3)

# Heart rate component
if len(emd_hr_signal) > 0:
    hr_segment = emd_hr_signal[:len(time_segment)]
    hr_peaks_segment = emd_hr_peaks[emd_hr_peaks < len(time_segment)]
    
    axes[1].plot(time_segment, hr_segment, 'g-', alpha=0.8, label='Heart Rate Component')
    if len(hr_peaks_segment) > 0:
        axes[1].plot(time_segment[hr_peaks_segment], hr_segment[hr_peaks_segment], 
                     'ro', markersize=8, label=f'HR Peaks ({len(emd_hr_peaks)} total)')
    axes[1].set_title('EMD Heart Rate Component', fontsize=14)
else:
    axes[1].plot(time_segment, np.zeros_like(time_segment), 'g-')
    axes[1].set_title('No Heart Rate Component Detected', fontsize=14)

axes[1].set_ylabel('Amplitude')
axes[1].legend()
axes[1].grid(True, alpha=0.3)

# Respiration component
if len(emd_rr_signal) > 0:
    rr_segment = emd_rr_signal[:len(time_segment)]
    rr_peaks_segment = emd_rr_peaks[emd_rr_peaks < len(time_segment)]
    
    axes[2].plot(time_segment, rr_segment, 'm-', alpha=0.8, label='Respiration Component')
    if len(rr_peaks_segment) > 0:
        axes[2].plot(time_segment[rr_peaks_segment], rr_segment[rr_peaks_segment], 
                     'bo', markersize=10, label=f'RR Peaks ({len(emd_rr_peaks)} total)')
    axes[2].set_title('EMD Respiration Component', fontsize=14)
else:
    axes[2].plot(time_segment, np.zeros_like(time_segment), 'm-')
    axes[2].set_title('No Respiration Component Detected', fontsize=14)

axes[2].set_xlabel('Time (seconds)')
axes[2].set_ylabel('Amplitude')
axes[2].legend()
axes[2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("=== COMPREHENSIVE ANALYSIS COMPARISON ===")
print()

# Run comprehensive analysis
all_results = comprehensive_ppg_analysis(ppg_signal, fs, methods=['FFT', 'Wavelet', 'EMD'])

# Extract results for comparison
methods_comparison = {
    'FFT': {
        'heart_rate': all_results['fft']['heart_rate'],
        'num_peaks': len(all_results['fft']['peaks']),
        'hr_std': np.std(all_results['fft']['instantaneous_hr']) if all_results['fft']['instantaneous_hr'] else 0
    },
    'Wavelet': {
        'heart_rate': all_results['wavelet']['heart_rate'],
        'num_peaks': len(all_results['wavelet']['heart_peaks']),
        'hr_std': np.std(all_results['wavelet']['instantaneous_hr']) if all_results['wavelet']['instantaneous_hr'] else 0,
        'resp_rate': all_results['wavelet']['resp_rate'],
        'num_resp_peaks': len(all_results['wavelet']['resp_peaks'])
    },
    'EMD': {
        'heart_rate': all_results['emd']['heart_rate'],
        'num_peaks': len(all_results['emd']['heart_peaks']),
        'hr_std': np.std(all_results['emd']['instantaneous_hr']) if all_results['emd']['instantaneous_hr'] else 0,
        'resp_rate': all_results['emd']['resp_rate'],
        'num_resp_peaks': len(all_results['emd']['resp_peaks']),
        'num_imfs': len(all_results['emd']['imfs'])
    }
}

# Create comparison table
print("Method Comparison Results:")
print("=" * 80)
print(f"{'Method':<10} {'HR (bpm)':<10} {'HR Peaks':<10} {'HR Std':<10} {'RR (br/min)':<12} {'RR Peaks':<10}")
print("-" * 80)

for method, results in methods_comparison.items():
    hr = results['heart_rate']
    hr_peaks = results['num_peaks']
    hr_std = results['hr_std']
    rr = results.get('resp_rate', 'N/A')
    rr_peaks = results.get('num_resp_peaks', 'N/A')
    
    rr_str = f"{rr:.1f}" if isinstance(rr, (int, float)) else str(rr)
    rr_peaks_str = str(rr_peaks) if isinstance(rr_peaks, (int, float)) else str(rr_peaks)
    
    print(f"{method:<10} {hr:<10.1f} {hr_peaks:<10} {hr_std:<10.1f} {rr_str:<12} {rr_peaks_str:<10}")

print("\nAdditional EMD Information:")
print(f"- Number of IMFs: {methods_comparison['EMD']['num_imfs']}")
print(f"- Heart rate IMFs: {all_results['emd']['heart_rate_imfs']}")
print(f"- Respiration IMFs: {all_results['emd']['resp_rate_imfs']}")
print(f"- Noise IMFs: {all_results['emd']['noise_imfs']}")

# Plot comparison of all filtered/processed signals
plt.figure(figsize=(15, 12))

time_segment = time_axis[:min(1500, len(time_axis))]  # First 15 seconds for clarity

# Original signal
plt.subplot(4, 1, 1)
plt.plot(time_segment, ppg_signal[:len(time_segment)], 'k-', alpha=0.8, linewidth=1)
plt.title('Original PPG Signal', fontsize=14)
plt.ylabel('Amplitude')
plt.grid(True, alpha=0.3)

# FFT filtered
plt.subplot(4, 1, 2)
fft_segment = all_results['fft']['filtered_signal'][:len(time_segment)]
fft_peaks_segment = all_results['fft']['peaks'][all_results['fft']['peaks'] < len(time_segment)]
plt.plot(time_segment, fft_segment, 'b-', alpha=0.8, linewidth=1, label='FFT Filtered')
if len(fft_peaks_segment) > 0:
    plt.plot(time_segment[fft_peaks_segment], fft_segment[fft_peaks_segment], 
             'ro', markersize=6, label=f'HR: {all_results["fft"]["heart_rate"]:.1f} bpm')
plt.title('FFT Bandpass Filtered (0.7-4 Hz)', fontsize=14)
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)

# Wavelet denoised
plt.subplot(4, 1, 3)
wav_segment = all_results['wavelet']['denoised_signal'][:len(time_segment)]
wav_peaks_segment = all_results['wavelet']['heart_peaks'][all_results['wavelet']['heart_peaks'] < len(time_segment)]
plt.plot(time_segment, wav_segment, 'g-', alpha=0.8, linewidth=1, label='Wavelet Denoised')
if len(wav_peaks_segment) > 0:
    plt.plot(time_segment[wav_peaks_segment], wav_segment[wav_peaks_segment], 
             'ro', markersize=6, label=f'HR: {all_results["wavelet"]["heart_rate"]:.1f} bpm')
plt.title('Wavelet Denoised (bior3.9, 5 levels)', fontsize=14)
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)

# EMD reconstructed
plt.subplot(4, 1, 4)
if len(all_results['emd']['heart_rate_signal']) > 0:
    emd_segment = all_results['emd']['heart_rate_signal'][:len(time_segment)]
    emd_peaks_segment = all_results['emd']['heart_peaks'][all_results['emd']['heart_peaks'] < len(time_segment)]
    plt.plot(time_segment, emd_segment, 'm-', alpha=0.8, linewidth=1, label='EMD Reconstructed')
    if len(emd_peaks_segment) > 0:
        plt.plot(time_segment[emd_peaks_segment], emd_segment[emd_peaks_segment], 
                 'ro', markersize=6, label=f'HR: {all_results["emd"]["heart_rate"]:.1f} bpm')
else:
    plt.plot(time_segment, np.zeros_like(time_segment), 'm-', alpha=0.8, linewidth=1, label='No EMD HR Signal')

plt.title('EMD Heart Rate Component', fontsize=14)
plt.xlabel('Time (seconds)')
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("=== SUMMARY AND CONCLUSIONS ===")
print()

print("1. FOURIER TRANSFORM (FFT) METHOD:")
print("   Strengths:")
print("   - Simple and computationally efficient")
print("   - Good for isolating specific frequency bands")
print("   - Clear frequency domain visualization")
print("   Limitations:")
print("   - Assumes stationary signals")
print("   - May not handle non-linear components well")
print("   - Fixed frequency bands may miss adaptive features")
print()

print("2. WAVELET TRANSFORM METHOD:")
print("   Strengths:")
print("   - Excellent for noise reduction")
print("   - Time-frequency localization")
print("   - Adaptive thresholding")
print("   - Can detect both heart rate and respiration")
print("   Limitations:")
print("   - Wavelet selection affects performance")
print("   - Parameter tuning required")
print("   - May over-smooth important features")
print()

print("3. EMPIRICAL MODE DECOMPOSITION (EMD):")
print("   Strengths:")
print("   - Data-adaptive decomposition")
print("   - No predefined basis functions")
print("   - Can separate multiple physiological components")
print("   - Handles non-stationary and non-linear signals")
print("   Limitations:")
print("   - Computationally intensive")
print("   - Mode mixing issues")
print("   - Sensitive to noise")
print("   - IMF selection requires domain knowledge")
print()

# Performance metrics
print("PERFORMANCE COMPARISON:")
print(f"Expected heart rate: ~72 bpm (1.2 Hz)")
print(f"Expected respiration rate: ~15 breaths/min (0.25 Hz)")
print()

for method, results in methods_comparison.items():
    hr_error = abs(results['heart_rate'] - 72) if results['heart_rate'] > 0 else float('inf')
    print(f"{method} Method:")
    print(f"  - Heart rate accuracy: {hr_error:.1f} bpm error")
    print(f"  - Heart rate variability: {results['hr_std']:.1f} bpm")
    if 'resp_rate' in results and isinstance(results['resp_rate'], (int, float)):
        rr_error = abs(results['resp_rate'] - 15)
        print(f"  - Respiration rate accuracy: {rr_error:.1f} breaths/min error")
    print()

print("RECOMMENDATIONS:")
print("- Use FFT for quick heart rate estimation with clean signals")
print("- Use Wavelet for robust denoising and dual HR/RR detection")
print("- Use EMD for complex signals with multiple physiological components")
print("- Consider ensemble methods (EEMD/CEEMDAN) for noisy signals")
print("- Combine methods for robust multi-parameter estimation")