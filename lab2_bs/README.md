# PPG Signal Analysis: Fourier Transform, Wavelet Transform, and EMD

This project implements comprehensive PPG (Photoplethysmography) signal analysis using three different signal processing approaches:

1. **Fourier Transform (FFT)** - Bandpass filtering for heart rate extraction
2. **Wavelet Transform** - Noise reduction and respiration rate detection  
3. **Empirical Mode Decomposition (EMD)** - Signal decomposition for both heart rate and respiration rate

## 📁 Files Overview

### Core Files
- **`hw2_utils.py`** - Main utility functions for all three analysis methods
- **`ppg_analysis_demo.ipynb`** - Comprehensive Jupyter notebook demonstrating all functionality
- **`requirements.txt`** - Required Python packages
- **`test_imports.py`** - Script to test if all dependencies are installed
- **`README.md`** - This documentation file

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install numpy scipy matplotlib pandas PyWavelets PyEMD jupyter
```

### 2. Test Installation
```bash
python3 test_imports.py
```

### 3. Run the Demo
```bash
jupyter notebook ppg_analysis_demo.ipynb
```

## 📊 Task Implementation

### Task 1: Fourier Transform Analysis
**Objective**: Apply FFT to denoise PPG signal and extract heart rate

**Implementation**:
- `compute_fft_spectrum()` - Compute frequency spectrum
- `apply_fft_bandpass_filter()` - Bandpass filter (0.7-4 Hz for heart rate)
- `detect_heart_rate_from_peaks()` - Peak detection for heart rate calculation
- `plot_fft_analysis()` - Visualization of FFT results

**Key Features**:
- Frequency domain visualization
- Bandpass filtering for heart rate isolation
- Inverse FFT for signal reconstruction
- Peak detection and heart rate calculation

### Task 2: Wavelet Transform Analysis
**Objective**: Use wavelet transform for noise reduction and respiration rate detection

**Implementation**:
- `wavelet_denoise_ppg()` - Wavelet denoising using bior3.9 or db4
- `detect_respiration_from_ppg()` - Respiration rate detection from envelope
- `plot_wavelet_analysis()` - Visualization of wavelet decomposition

**Key Features**:
- Multi-level wavelet decomposition (5 levels)
- Soft/hard thresholding for noise reduction
- SURE, Bayes, or minimax threshold selection
- Both heart rate and respiration rate detection

### Task 3: Empirical Mode Decomposition (EMD)
**Objective**: Use EMD to decompose signal and extract both heart rate and respiration rate

**Implementation**:
- `emd_decompose_ppg()` - EMD/EEMD/CEEMDAN decomposition
- `select_relevant_imfs()` - Automatic IMF classification
- `reconstruct_from_selected_imfs()` - Signal reconstruction
- `plot_emd_analysis()` - Visualization of EMD results

**Key Features**:
- Data-adaptive signal decomposition
- Automatic IMF classification (heart rate, respiration, noise)
- Support for EMD, EEMD, and CEEMDAN variants
- Separate extraction of physiological components

## 🔧 Function Reference

### Core Analysis Functions

```python
# FFT-based analysis
fft_filtered = apply_fft_bandpass_filter(signal, 0.7, 4.0, sampling_freq)
peaks, hr, hr_inst = detect_heart_rate_from_peaks(fft_filtered, sampling_freq)

# Wavelet-based analysis
denoised, coeffs_orig, coeffs_thresh = wavelet_denoise_ppg(signal, wavelet='bior3.9')
resp_peaks, rr, rr_inst = detect_respiration_from_ppg(denoised, sampling_freq)

# EMD-based analysis
residue, imfs = emd_decompose_ppg(signal, method='EMD')
hr_imfs, rr_imfs, noise_imfs = select_relevant_imfs(imfs, sampling_freq)
reconstructed = reconstruct_from_selected_imfs(imfs, hr_imfs)

# Comprehensive analysis (all methods)
results = comprehensive_ppg_analysis(signal, sampling_freq, methods=['FFT', 'Wavelet', 'EMD'])
```

### Visualization Functions

```python
# Plot FFT analysis
plot_fft_analysis(original_signal, filtered_signal, sampling_freq)

# Plot wavelet analysis
plot_wavelet_analysis(original, denoised, coeffs_orig, coeffs_thresh, sampling_freq)

# Plot EMD analysis
plot_emd_analysis(original_signal, imfs, residue, sampling_freq)
```

## 📈 Expected Results

### Synthetic Data Parameters
- **Heart Rate**: ~72 bpm (1.2 Hz)
- **Respiration Rate**: ~15 breaths/min (0.25 Hz)
- **Sampling Frequency**: 100 Hz
- **Duration**: 60 seconds

### Performance Metrics
- Heart rate detection accuracy
- Respiration rate detection accuracy
- Signal-to-noise ratio improvement
- Computational efficiency comparison

## 🔬 Method Comparison

| Method | Strengths | Limitations | Best Use Case |
|--------|-----------|-------------|---------------|
| **FFT** | Fast, simple, clear frequency visualization | Assumes stationarity, fixed bands | Clean signals, quick HR estimation |
| **Wavelet** | Excellent denoising, time-frequency localization | Parameter tuning, wavelet selection | Noisy signals, dual HR/RR detection |
| **EMD** | Data-adaptive, handles non-stationarity | Computationally intensive, mode mixing | Complex signals, multiple components |

## 📝 Usage Examples

### Basic Usage
```python
from hw2_utils import *

# Load your PPG data (replace with actual data loading)
ppg_signal, fs = load_ppg_data_sample(duration=60, sampling_freq=100)

# Method 1: FFT Analysis
fft_filtered = apply_fft_bandpass_filter(ppg_signal, 0.7, 4.0, fs)
hr_peaks, mean_hr, _ = detect_heart_rate_from_peaks(fft_filtered, fs)
print(f"FFT Heart Rate: {mean_hr:.1f} bpm")

# Method 2: Wavelet Analysis
denoised_signal, _, _ = wavelet_denoise_ppg(ppg_signal)
hr_peaks, mean_hr, _ = detect_heart_rate_from_peaks(denoised_signal, fs)
resp_peaks, mean_rr, _ = detect_respiration_from_ppg(denoised_signal, fs)
print(f"Wavelet HR: {mean_hr:.1f} bpm, RR: {mean_rr:.1f} br/min")

# Method 3: EMD Analysis
residue, imfs = emd_decompose_ppg(ppg_signal)
hr_imfs, rr_imfs, _ = select_relevant_imfs(imfs, fs)
hr_signal = reconstruct_from_selected_imfs(imfs, hr_imfs)
print(f"EMD detected {len(hr_imfs)} heart rate IMFs")
```

### Advanced Usage
```python
# Comprehensive analysis with all methods
results = comprehensive_ppg_analysis(ppg_signal, fs)

# Access results
fft_hr = results['fft']['heart_rate']
wavelet_hr = results['wavelet']['heart_rate']
wavelet_rr = results['wavelet']['resp_rate']
emd_hr = results['emd']['heart_rate']
emd_rr = results['emd']['resp_rate']

print(f"Heart Rate - FFT: {fft_hr:.1f}, Wavelet: {wavelet_hr:.1f}, EMD: {emd_hr:.1f} bpm")
print(f"Respiration Rate - Wavelet: {wavelet_rr:.1f}, EMD: {emd_rr:.1f} br/min")
```

## 🔄 Integration with Real Data

To use with actual PPG DaLia dataset, replace the `load_ppg_data_sample()` function:

```python
def load_ppg_dalia_data(subject_id, activity, duration=None):
    """
    Load actual PPG data from DaLia dataset.
    Implement according to your dataset structure.
    """
    # Your data loading implementation here
    # Return: ppg_signal (numpy array), sampling_freq (float)
    pass
```

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**: Run `python3 test_imports.py` to check dependencies
2. **Memory Issues**: Reduce signal duration or use EEMD with fewer trials
3. **No Peaks Detected**: Adjust threshold parameters or check signal quality
4. **EMD Convergence**: Try EEMD or CEEMDAN for noisy signals

### Performance Tips

- Use FFT for real-time applications
- Use Wavelet for moderate noise levels
- Use EMD for research/offline analysis
- Combine methods for robust estimation

## 📚 References

- Fourier Transform: Classical frequency domain analysis
- Wavelet Transform: Daubechies, I. (1992). Ten Lectures on Wavelets
- EMD: Huang, N. E., et al. (1998). The empirical mode decomposition and the Hilbert spectrum
- PPG Analysis: Allen, J. (2007). Photoplethysmography and its application in clinical physiological measurement

## 🤝 Contributing

Feel free to extend the functionality by:
- Adding new wavelet types
- Implementing advanced EMD variants
- Adding machine learning-based peak detection
- Integrating with real-time data streams

---

**Note**: This implementation uses synthetic data for demonstration. For production use, integrate with actual PPG DaLia dataset and validate against ground truth measurements.
