#!/usr/bin/env python3
"""
Test script to check if all required modules can be imported.
Run this before using hw2_utils.py to ensure all dependencies are available.
"""

def test_imports():
    """Test if all required modules can be imported."""
    required_modules = [
        'numpy',
        'matplotlib',
        'scipy',
        'pandas',
        'pywt',
        'PyEMD'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} - OK")
        except ImportError:
            print(f"✗ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\nMissing modules: {missing_modules}")
        print("Install them using:")
        print("pip install numpy scipy matplotlib pandas PyWavelets PyEMD")
        return False
    else:
        print("\n✓ All required modules are available!")
        return True

def test_hw2_utils():
    """Test if hw2_utils.py can be imported."""
    try:
        import hw2_utils
        print("✓ hw2_utils.py imported successfully!")
        
        # List available functions
        functions = [func for func in dir(hw2_utils) if not func.startswith('_')]
        print(f"Available functions ({len(functions)}):")
        for func in sorted(functions):
            print(f"  - {func}")
        
        return True
    except ImportError as e:
        print(f"✗ Failed to import hw2_utils.py: {e}")
        return False

if __name__ == "__main__":
    print("Testing PPG Analysis Dependencies")
    print("=" * 40)
    
    # Test basic imports
    imports_ok = test_imports()
    
    print("\n" + "=" * 40)
    
    # Test hw2_utils if basic imports work
    if imports_ok:
        hw2_utils_ok = test_hw2_utils()
        
        if hw2_utils_ok:
            print("\n🎉 Everything is ready! You can now run the PPG analysis.")
        else:
            print("\n❌ hw2_utils.py has issues. Check the code for errors.")
    else:
        print("\n❌ Install missing dependencies first.")
        print("Then run this script again to verify the installation.")
