#!/usr/bin/env python3
"""
Test script to verify PPG data loading and analysis with real PPG_FieldStudy data
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the parent directory to path to import hw2_utils
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from hw2_utils import (
        load_ppg_fieldstudy_data,
        load_ppg_data_sample,
        apply_fft_bandpass_filter,
        detect_heart_rate_from_peaks,
        comprehensive_ppg_analysis
    )
    print("✓ Successfully imported hw2_utils functions")
except ImportError as e:
    print(f"✗ Failed to import hw2_utils: {e}")
    print("Using basic functions only...")

def test_data_loading():
    """Test loading real PPG data"""
    print("\n=== TESTING DATA LOADING ===")
    
    try:
        # Test E4 data loading
        print("Loading E4 data...")
        ppg_e4, fs_e4, metadata_e4 = load_ppg_fieldstudy_data(
            subject_id="S1",
            data_source="E4",
            duration=60,  # Load 1 minute
            start_time=100  # Start after 100 seconds
        )
        
        print(f"✓ E4 data loaded successfully!")
        print(f"  - Shape: {ppg_e4.shape}")
        print(f"  - Sampling frequency: {fs_e4} Hz")
        print(f"  - Duration: {metadata_e4['duration']:.2f} seconds")
        print(f"  - Data source: {metadata_e4['data_source']}")
        print(f"  - Signal range: {np.min(ppg_e4):.3f} to {np.max(ppg_e4):.3f}")
        print(f"  - Signal mean: {np.mean(ppg_e4):.3f}, std: {np.std(ppg_e4):.3f}")
        
        if 'subject_info' in metadata_e4:
            print(f"  - Subject info: {metadata_e4['subject_info']}")
        
        # Test RespiBAN data loading
        print("\nLoading RespiBAN data...")
        ppg_respiban, fs_respiban, metadata_respiban = load_ppg_fieldstudy_data(
            subject_id="S1",
            data_source="RespiBAN",
            duration=60,  # Load 1 minute
            start_time=100  # Start after 100 seconds
        )
        
        print(f"✓ RespiBAN data loaded successfully!")
        print(f"  - Shape: {ppg_respiban.shape}")
        print(f"  - Sampling frequency: {fs_respiban} Hz")
        print(f"  - Duration: {metadata_respiban['duration']:.2f} seconds")
        print(f"  - Data source: {metadata_respiban['data_source']}")
        print(f"  - Signal range: {np.min(ppg_respiban):.3f} to {np.max(ppg_respiban):.3f}")
        print(f"  - Signal mean: {np.mean(ppg_respiban):.3f}, std: {np.std(ppg_respiban):.3f}")
        
        return ppg_e4, fs_e4, metadata_e4, ppg_respiban, fs_respiban, metadata_respiban
        
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return None, None, None, None, None, None

def test_signal_analysis(ppg_signal, fs, data_source_name):
    """Test signal analysis on real data"""
    print(f"\n=== TESTING {data_source_name} SIGNAL ANALYSIS ===")
    
    try:
        # Basic FFT filtering
        print("Applying FFT bandpass filter...")
        filtered_signal = apply_fft_bandpass_filter(ppg_signal, 0.7, 4.0, fs)
        print(f"✓ FFT filtering successful")
        print(f"  - Original signal std: {np.std(ppg_signal):.3f}")
        print(f"  - Filtered signal std: {np.std(filtered_signal):.3f}")
        
        # Heart rate detection
        print("Detecting heart rate...")
        peaks, mean_hr, instantaneous_hr = detect_heart_rate_from_peaks(filtered_signal, fs)
        print(f"✓ Heart rate detection successful")
        print(f"  - Detected {len(peaks)} peaks")
        print(f"  - Mean heart rate: {mean_hr:.1f} bpm")
        if len(instantaneous_hr) > 0:
            print(f"  - HR range: {np.min(instantaneous_hr):.1f} - {np.max(instantaneous_hr):.1f} bpm")
            print(f"  - HR std: {np.std(instantaneous_hr):.1f} bpm")
        
        # Check if heart rate is reasonable
        if 40 <= mean_hr <= 200:
            print(f"✓ Heart rate is within reasonable range")
        else:
            print(f"⚠ Heart rate seems unusual: {mean_hr:.1f} bpm")
        
        return filtered_signal, peaks, mean_hr
        
    except Exception as e:
        print(f"✗ Error in signal analysis: {e}")
        return None, None, None

def plot_signal_comparison(ppg_e4, fs_e4, ppg_respiban, fs_respiban):
    """Plot comparison of E4 vs RespiBAN signals"""
    print("\n=== PLOTTING SIGNAL COMPARISON ===")
    
    try:
        # Create time axes
        duration = min(30, len(ppg_e4)/fs_e4, len(ppg_respiban)/fs_respiban)  # 30 seconds max
        
        samples_e4 = int(duration * fs_e4)
        samples_respiban = int(duration * fs_respiban)
        
        time_e4 = np.arange(samples_e4) / fs_e4
        time_respiban = np.arange(samples_respiban) / fs_respiban
        
        plt.figure(figsize=(15, 8))
        
        # E4 signal
        plt.subplot(2, 1, 1)
        plt.plot(time_e4, ppg_e4[:samples_e4], 'b-', alpha=0.8, linewidth=1)
        plt.title(f'E4 PPG Signal (fs={fs_e4} Hz)', fontsize=14)
        plt.ylabel('Amplitude')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, duration)
        
        # RespiBAN signal
        plt.subplot(2, 1, 2)
        plt.plot(time_respiban, ppg_respiban[:samples_respiban], 'r-', alpha=0.8, linewidth=1)
        plt.title(f'RespiBAN PPG Signal (fs={fs_respiban} Hz)', fontsize=14)
        plt.xlabel('Time (seconds)')
        plt.ylabel('Amplitude')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, duration)
        
        plt.tight_layout()
        plt.savefig('ppg_comparison.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ Signal comparison plot created and saved as 'ppg_comparison.png'")
        
    except Exception as e:
        print(f"✗ Error creating plots: {e}")

def test_comprehensive_analysis(ppg_signal, fs, data_source_name):
    """Test comprehensive analysis if available"""
    print(f"\n=== TESTING COMPREHENSIVE ANALYSIS ({data_source_name}) ===")
    
    try:
        # Use a shorter segment for faster processing
        duration = min(30, len(ppg_signal) / fs)  # 30 seconds max
        samples = int(duration * fs)
        signal_segment = ppg_signal[:samples]
        
        print(f"Running comprehensive analysis on {duration:.1f} seconds of data...")
        
        # Test with FFT only first (fastest)
        results = comprehensive_ppg_analysis(signal_segment, fs, methods=['FFT'])
        
        print("✓ Comprehensive analysis successful")
        print(f"  - FFT Heart Rate: {results['fft']['heart_rate']:.1f} bpm")
        print(f"  - FFT Peaks detected: {len(results['fft']['peaks'])}")
        
        return results
        
    except Exception as e:
        print(f"✗ Error in comprehensive analysis: {e}")
        return None

def main():
    """Main test function"""
    print("Testing PPG_FieldStudy Data Loading and Analysis")
    print("=" * 60)
    
    # Test data loading
    ppg_e4, fs_e4, meta_e4, ppg_respiban, fs_respiban, meta_respiban = test_data_loading()
    
    if ppg_e4 is None or ppg_respiban is None:
        print("❌ Data loading failed. Cannot proceed with analysis.")
        return
    
    # Test signal analysis on both datasets
    if ppg_e4 is not None:
        filtered_e4, peaks_e4, hr_e4 = test_signal_analysis(ppg_e4, fs_e4, "E4")
    
    if ppg_respiban is not None:
        filtered_respiban, peaks_respiban, hr_respiban = test_signal_analysis(ppg_respiban, fs_respiban, "RespiBAN")
    
    # Plot comparison
    if ppg_e4 is not None and ppg_respiban is not None:
        plot_signal_comparison(ppg_e4, fs_e4, ppg_respiban, fs_respiban)
    
    # Test comprehensive analysis
    if ppg_e4 is not None:
        results_e4 = test_comprehensive_analysis(ppg_e4, fs_e4, "E4")
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"✓ E4 data: {len(ppg_e4)} samples at {fs_e4} Hz, HR: {hr_e4:.1f} bpm")
    print(f"✓ RespiBAN data: {len(ppg_respiban)} samples at {fs_respiban} Hz, HR: {hr_respiban:.1f} bpm")
    print("✓ All tests completed successfully!")
    print("\nThe PPG data loading and analysis functions are working correctly with real data.")

if __name__ == "__main__":
    main()
