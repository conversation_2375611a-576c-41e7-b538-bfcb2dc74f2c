#!/usr/bin/env python3
"""
Simple test script to verify PPG data loading without heavy dependencies
"""

import numpy as np
import pandas as pd
import zipfile
import h5py
import os

def load_ppg_fieldstudy_data_simple(
    subject_id: str = "S1",
    data_source: str = "E4",
    duration: float = None,
    start_time: float = 0,
    data_path: str = "../data/PPG_FieldStudy"
):
    """
    Simple version of PPG data loading function
    """
    subject_path = os.path.join(data_path, subject_id)
    
    # Load metadata
    metadata = {}
    
    # Load activity data
    activity_file = os.path.join(subject_path, f"{subject_id}_activity.csv")
    if os.path.exists(activity_file):
        activity_df = pd.read_csv(activity_file)
        activities = []
        for _, row in activity_df.iterrows():
            activity_name = row.iloc[0].strip().replace("# ", "")
            timestamp = row.iloc[1]
            if activity_name != "SUBJECT_ID":
                activities.append({"activity": activity_name, "timestamp": timestamp})
        metadata["activities"] = activities
    
    # Load subject info
    quest_file = os.path.join(subject_path, f"{subject_id}_quest.csv")
    if os.path.exists(quest_file):
        quest_df = pd.read_csv(quest_file)
        subject_info = {}
        for _, row in quest_df.iterrows():
            key = row.iloc[0].strip().replace("# ", "")
            value = row.iloc[1]
            if key != "SUBJECT_ID":
                subject_info[key.lower()] = value
        metadata["subject_info"] = subject_info
    
    if data_source == "E4":
        # Load E4 BVP data
        e4_file = os.path.join(subject_path, f"{subject_id}_E4.zip")
        
        with zipfile.ZipFile(e4_file, "r") as z:
            # Read BVP data
            with z.open("BVP.csv") as f:
                lines = f.read().decode("utf-8").strip().split("\n")
            
            # First line is start timestamp, second line is sampling frequency
            start_timestamp = float(lines[0])
            sampling_freq = float(lines[1])
            
            # Rest are PPG values
            ppg_values = np.array([float(line) for line in lines[2:]])
            
            metadata["start_timestamp"] = start_timestamp
            metadata["data_source"] = "E4_BVP"
    
    elif data_source == "RespiBAN":
        # Load RespiBAN data
        respiban_file = os.path.join(subject_path, f"{subject_id}_RespiBAN.h5")
        
        with h5py.File(respiban_file, "r") as f:
            # Get the device group (MAC address)
            device_key = list(f.keys())[0]
            device_group = f[device_key]
            
            # Get raw data
            raw_group = device_group["raw"]
            
            # Try different channels to find the best PPG signal
            best_channel = None
            best_variance = 0
            
            for channel_name in ["channel_1", "channel_5", "channel_6", "channel_7", "channel_8"]:
                if channel_name in raw_group:
                    channel_data = raw_group[channel_name][:]
                    if np.var(channel_data) > best_variance:
                        best_variance = np.var(channel_data)
                        best_channel = channel_name
            
            if best_channel is None:
                raise ValueError("No suitable PPG channel found in RespiBAN data")
            
            ppg_values = raw_group[best_channel][:].flatten()
            
            # RespiBAN typically samples at 700 Hz
            sampling_freq = 700.0
            
            metadata["data_source"] = f"RespiBAN_{best_channel}"
            metadata["device_id"] = device_key
    
    else:
        raise ValueError("data_source must be 'E4' or 'RespiBAN'")
    
    # Apply time windowing if requested
    if start_time > 0 or duration is not None:
        start_sample = int(start_time * sampling_freq)
        
        if duration is not None:
            end_sample = start_sample + int(duration * sampling_freq)
            end_sample = min(end_sample, len(ppg_values))
        else:
            end_sample = len(ppg_values)
        
        ppg_values = ppg_values[start_sample:end_sample]
    
    # Convert to float and normalize if needed
    ppg_signal = ppg_values.astype(np.float64)
    
    # For RespiBAN data, convert from uint16 to meaningful values
    if data_source == "RespiBAN":
        # Simple normalization - center around zero
        ppg_signal = ppg_signal - np.mean(ppg_signal)
        ppg_signal = ppg_signal / np.std(ppg_signal)
    
    metadata["sampling_freq"] = sampling_freq
    metadata["duration"] = len(ppg_signal) / sampling_freq
    metadata["num_samples"] = len(ppg_signal)
    
    return ppg_signal, sampling_freq, metadata

def simple_heart_rate_detection(signal, sampling_freq):
    """
    Simple heart rate detection using basic peak finding
    """
    from scipy.signal import find_peaks
    
    # Simple bandpass filter approximation
    # Remove DC component
    signal_filtered = signal - np.mean(signal)
    
    # Find peaks with minimum distance (assume max 200 bpm = 3.33 Hz)
    min_distance = int(sampling_freq * 0.3)  # 0.3 seconds minimum between peaks
    
    # Find peaks above mean + 0.5*std
    threshold = np.mean(signal_filtered) + 0.5 * np.std(signal_filtered)
    peaks, _ = find_peaks(signal_filtered, height=threshold, distance=min_distance)
    
    if len(peaks) < 2:
        return peaks, 0, []
    
    # Calculate heart rate
    peak_intervals = np.diff(peaks) / sampling_freq
    instantaneous_hr = 60.0 / peak_intervals
    mean_hr = np.mean(instantaneous_hr)
    
    return peaks, mean_hr, instantaneous_hr.tolist()

def test_data_loading():
    """Test loading real PPG data"""
    print("=== TESTING DATA LOADING ===")
    
    try:
        # Test E4 data loading
        print("Loading E4 data...")
        ppg_e4, fs_e4, metadata_e4 = load_ppg_fieldstudy_data_simple(
            subject_id="S1",
            data_source="E4",
            duration=60,  # Load 1 minute
            start_time=100  # Start after 100 seconds
        )
        
        print(f"✓ E4 data loaded successfully!")
        print(f"  - Shape: {ppg_e4.shape}")
        print(f"  - Sampling frequency: {fs_e4} Hz")
        print(f"  - Duration: {metadata_e4['duration']:.2f} seconds")
        print(f"  - Data source: {metadata_e4['data_source']}")
        print(f"  - Signal range: {np.min(ppg_e4):.3f} to {np.max(ppg_e4):.3f}")
        print(f"  - Signal mean: {np.mean(ppg_e4):.3f}, std: {np.std(ppg_e4):.3f}")
        
        if 'subject_info' in metadata_e4:
            print(f"  - Subject info: {metadata_e4['subject_info']}")
        
        # Test heart rate detection on E4 data
        print("  - Testing heart rate detection...")
        peaks_e4, hr_e4, hr_inst_e4 = simple_heart_rate_detection(ppg_e4, fs_e4)
        print(f"    Detected {len(peaks_e4)} peaks, HR: {hr_e4:.1f} bpm")
        
        # Test RespiBAN data loading
        print("\nLoading RespiBAN data...")
        ppg_respiban, fs_respiban, metadata_respiban = load_ppg_fieldstudy_data_simple(
            subject_id="S1",
            data_source="RespiBAN",
            duration=60,  # Load 1 minute
            start_time=100  # Start after 100 seconds
        )
        
        print(f"✓ RespiBAN data loaded successfully!")
        print(f"  - Shape: {ppg_respiban.shape}")
        print(f"  - Sampling frequency: {fs_respiban} Hz")
        print(f"  - Duration: {metadata_respiban['duration']:.2f} seconds")
        print(f"  - Data source: {metadata_respiban['data_source']}")
        print(f"  - Signal range: {np.min(ppg_respiban):.3f} to {np.max(ppg_respiban):.3f}")
        print(f"  - Signal mean: {np.mean(ppg_respiban):.3f}, std: {np.std(ppg_respiban):.3f}")
        
        # Test heart rate detection on RespiBAN data
        print("  - Testing heart rate detection...")
        peaks_respiban, hr_respiban, hr_inst_respiban = simple_heart_rate_detection(ppg_respiban, fs_respiban)
        print(f"    Detected {len(peaks_respiban)} peaks, HR: {hr_respiban:.1f} bpm")
        
        return True
        
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_subjects():
    """Test loading data from multiple subjects"""
    print("\n=== TESTING MULTIPLE SUBJECTS ===")
    
    subjects = ["S1", "S2", "S3"]
    
    for subject in subjects:
        try:
            print(f"\nTesting {subject}...")
            ppg, fs, metadata = load_ppg_fieldstudy_data_simple(
                subject_id=subject,
                data_source="E4",
                duration=30,  # 30 seconds
                start_time=50
            )
            
            peaks, hr, _ = simple_heart_rate_detection(ppg, fs)
            
            print(f"  ✓ {subject}: {len(ppg)} samples, {fs} Hz, HR: {hr:.1f} bpm")
            
            if 'subject_info' in metadata:
                age = metadata['subject_info'].get('age', 'N/A')
                gender = metadata['subject_info'].get('gender', 'N/A')
                print(f"    Subject: {age} years old, {gender}")
            
        except Exception as e:
            print(f"  ✗ {subject}: Error - {e}")

def main():
    """Main test function"""
    print("Testing PPG_FieldStudy Data Loading (Simple Version)")
    print("=" * 60)
    
    # Test basic data loading
    success = test_data_loading()
    
    if success:
        # Test multiple subjects
        test_multiple_subjects()
        
        print("\n" + "=" * 60)
        print("✓ All tests completed successfully!")
        print("✓ PPG data loading function is working correctly with real data.")
        print("✓ Heart rate detection is producing reasonable results.")
        print("\nNext steps:")
        print("1. Install missing dependencies: pip install PyWavelets PyEMD")
        print("2. Run the full analysis with hw2_utils.py")
        print("3. Use the Jupyter notebook for comprehensive analysis")
    else:
        print("\n❌ Data loading failed. Check file paths and data integrity.")

if __name__ == "__main__":
    main()
