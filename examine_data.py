#!/usr/bin/env python3
"""
Script to examine the PPG_FieldStudy data structure
"""

import pandas as pd
import numpy as np
import h5py
import pickle
import os
import zipfile

def examine_csv_files():
    """Examine CSV files structure"""
    print("=== CSV FILES EXAMINATION ===")
    
    # Activity data
    activity_df = pd.read_csv('data/PPG_FieldStudy/S1/S1_activity.csv')
    print("Activity CSV:")
    print(activity_df)
    print()
    
    # Quest data
    quest_df = pd.read_csv('data/PPG_FieldStudy/S1/S1_quest.csv')
    print("Quest CSV:")
    print(quest_df)
    print()

def examine_hdf5_file():
    """Examine HDF5 file structure"""
    print("=== HDF5 FILE EXAMINATION ===")
    
    try:
        with h5py.File('data/PPG_FieldStudy/S1/S1_RespiBAN.h5', 'r') as f:
            print("HDF5 root keys:", list(f.keys()))
            
            for key in f.keys():
                item = f[key]
                if isinstance(item, h5py.Dataset):
                    print(f"{key}: Dataset - shape {item.shape}, dtype {item.dtype}")
                    if item.size < 20:
                        print(f"  Values: {item[...]}")
                    else:
                        print(f"  First 10 values: {item[:10]}")
                        print(f"  Last 10 values: {item[-10:]}")
                elif isinstance(item, h5py.Group):
                    print(f"{key}: Group")
                    for subkey in item.keys():
                        subitem = item[subkey]
                        if isinstance(subitem, h5py.Dataset):
                            print(f"  {subkey}: shape {subitem.shape}, dtype {subitem.dtype}")
                            if subitem.size < 20:
                                print(f"    Values: {subitem[...]}")
                            else:
                                print(f"    First 5 values: {subitem[:5]}")
                print()
    except Exception as e:
        print(f"Error reading HDF5: {e}")

def examine_zip_file():
    """Examine ZIP file contents"""
    print("=== ZIP FILE EXAMINATION ===")
    
    try:
        with zipfile.ZipFile('data/PPG_FieldStudy/S1/S1_E4.zip', 'r') as z:
            print("ZIP file contents:")
            for filename in z.namelist():
                print(f"  {filename}")
                
            # Try to read a CSV file from the zip
            csv_files = [f for f in z.namelist() if f.endswith('.csv')]
            if csv_files:
                print(f"\nReading {csv_files[0]}:")
                with z.open(csv_files[0]) as f:
                    df = pd.read_csv(f)
                    print(f"Shape: {df.shape}")
                    print(f"Columns: {list(df.columns)}")
                    print("First few rows:")
                    print(df.head())
    except Exception as e:
        print(f"Error reading ZIP: {e}")

def examine_pickle_file():
    """Examine pickle file"""
    print("=== PICKLE FILE EXAMINATION ===")
    
    try:
        # Try different encodings
        for encoding in ['latin1', 'bytes', None]:
            try:
                print(f"Trying encoding: {encoding}")
                with open('data/PPG_FieldStudy/S1/S1.pkl', 'rb') as f:
                    if encoding:
                        data = pickle.load(f, encoding=encoding)
                    else:
                        data = pickle.load(f)
                
                print(f"Successfully loaded with encoding: {encoding}")
                print(f"Data type: {type(data)}")
                
                if isinstance(data, dict):
                    print("Keys:", list(data.keys()))
                    for key, value in data.items():
                        if hasattr(value, 'shape'):
                            print(f"  {key}: shape {value.shape}, dtype {value.dtype}")
                        else:
                            print(f"  {key}: {type(value)}")
                elif hasattr(data, 'shape'):
                    print(f"Shape: {data.shape}")
                    print(f"Dtype: {data.dtype}")
                
                break  # Success, exit loop
                
            except Exception as e:
                print(f"Failed with encoding {encoding}: {e}")
                continue
    except Exception as e:
        print(f"Error reading pickle: {e}")

if __name__ == "__main__":
    print("Examining PPG_FieldStudy data structure...")
    print("=" * 50)
    
    examine_csv_files()
    examine_hdf5_file()
    examine_zip_file()
    examine_pickle_file()
