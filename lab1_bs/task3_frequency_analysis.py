"""
Task 3: Perform a Fourier Transform on the filtered ECG signal and plot the frequency spectrum.

This module implements:
1. Import necessary libraries for computing the Fourier Transform (fft, fftfreq)
2. Calculate the Fourier Transform of the filtered ECG signal
3. Convert the frequencies to a human-readable form and identify the dominant frequencies
4. Plot the magnitude spectrum of the frequencies
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq, fftshift
from typing import Tuple, List
import pandas as pd
from task1_butterworth_filter import load_ecg_data, design_butterworth_filter, apply_filter


def compute_fft(signal: np.ndarray, sampling_freq: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Compute the Fast Fourier Transform of the signal.
    
    Args:
        signal: Input signal
        sampling_freq: Sampling frequency in Hz
        
    Returns:
        Tuple of (frequencies, fft_magnitude, fft_complex)
    """
    # Number of samples
    N = len(signal)
    
    # Compute FFT
    fft_complex = fft(signal)
    
    # Compute frequency bins
    frequencies = fftfreq(N, 1/sampling_freq)
    
    # Compute magnitude spectrum
    fft_magnitude = np.abs(fft_complex)
    
    return frequencies, fft_magnitude, fft_complex


def compute_power_spectral_density(signal: np.ndarray, sampling_freq: float) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute the Power Spectral Density (PSD) of the signal.
    
    Args:
        signal: Input signal
        sampling_freq: Sampling frequency in Hz
        
    Returns:
        Tuple of (frequencies, psd)
    """
    frequencies, fft_magnitude, _ = compute_fft(signal, sampling_freq)
    
    # Compute PSD (magnitude squared, normalized)
    N = len(signal)
    psd = (fft_magnitude ** 2) / (N * sampling_freq)
    
    return frequencies, psd


def find_dominant_frequencies(frequencies: np.ndarray, magnitude: np.ndarray, 
                            num_peaks: int = 5, freq_range: Tuple[float, float] = (0.5, 50)) -> List[Tuple[float, float]]:
    """
    Find the dominant frequencies in the spectrum.
    
    Args:
        frequencies: Frequency array
        magnitude: Magnitude spectrum
        num_peaks: Number of dominant frequencies to find
        freq_range: Frequency range to consider (min_freq, max_freq)
        
    Returns:
        List of tuples (frequency, magnitude) for dominant frequencies
    """
    # Consider only positive frequencies within the specified range
    positive_freq_mask = (frequencies >= freq_range[0]) & (frequencies <= freq_range[1])
    freq_subset = frequencies[positive_freq_mask]
    mag_subset = magnitude[positive_freq_mask]
    
    # Find peaks in the magnitude spectrum
    from scipy.signal import find_peaks
    
    # Find peaks with some minimum prominence
    peak_indices, _ = find_peaks(mag_subset, prominence=np.max(mag_subset) * 0.1)
    
    if len(peak_indices) == 0:
        # If no peaks found, just take the maximum
        max_idx = np.argmax(mag_subset)
        return [(freq_subset[max_idx], mag_subset[max_idx])]
    
    # Sort peaks by magnitude (descending)
    peak_magnitudes = mag_subset[peak_indices]
    sorted_indices = np.argsort(peak_magnitudes)[::-1]
    
    # Return top peaks
    dominant_freqs = []
    for i in range(min(num_peaks, len(sorted_indices))):
        idx = peak_indices[sorted_indices[i]]
        dominant_freqs.append((freq_subset[idx], mag_subset[idx]))
    
    return dominant_freqs


def plot_frequency_spectrum(frequencies: np.ndarray, magnitude: np.ndarray,
                          title: str = "Frequency Spectrum", 
                          freq_limit: float = 100, log_scale: bool = False) -> None:
    """
    Plot the frequency spectrum.
    
    Args:
        frequencies: Frequency array
        magnitude: Magnitude spectrum
        title: Plot title
        freq_limit: Maximum frequency to display
        log_scale: Whether to use logarithmic scale for magnitude
    """
    # Consider only positive frequencies up to freq_limit
    positive_mask = (frequencies >= 0) & (frequencies <= freq_limit)
    freq_plot = frequencies[positive_mask]
    mag_plot = magnitude[positive_mask]
    
    plt.figure(figsize=(12, 6))
    
    if log_scale:
        plt.semilogy(freq_plot, mag_plot, 'b-', linewidth=1)
        plt.ylabel('Magnitude (log scale)')
    else:
        plt.plot(freq_plot, mag_plot, 'b-', linewidth=1)
        plt.ylabel('Magnitude')
    
    plt.xlabel('Frequency (Hz)')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def plot_power_spectral_density(frequencies: np.ndarray, psd: np.ndarray,
                               title: str = "Power Spectral Density",
                               freq_limit: float = 100) -> None:
    """
    Plot the Power Spectral Density.
    
    Args:
        frequencies: Frequency array
        psd: Power spectral density
        title: Plot title
        freq_limit: Maximum frequency to display
    """
    # Consider only positive frequencies up to freq_limit
    positive_mask = (frequencies >= 0) & (frequencies <= freq_limit)
    freq_plot = frequencies[positive_mask]
    psd_plot = psd[positive_mask]
    
    plt.figure(figsize=(12, 6))
    plt.semilogy(freq_plot, psd_plot, 'r-', linewidth=1)
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Power Spectral Density (V²/Hz)')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def analyze_ecg_frequency_bands(frequencies: np.ndarray, psd: np.ndarray) -> dict:
    """
    Analyze ECG signal in different frequency bands.
    
    Args:
        frequencies: Frequency array
        psd: Power spectral density
        
    Returns:
        Dictionary with power in different frequency bands
    """
    # Define ECG frequency bands
    bands = {
        'Very Low Frequency (VLF)': (0.003, 0.04),  # 0.003-0.04 Hz
        'Low Frequency (LF)': (0.04, 0.15),        # 0.04-0.15 Hz
        'High Frequency (HF)': (0.15, 0.4),        # 0.15-0.4 Hz
        'QRS Complex': (10, 30),                    # 10-30 Hz (main QRS energy)
        'High Frequency Noise': (30, 100)          # 30-100 Hz
    }
    
    band_powers = {}
    
    # Consider only positive frequencies
    positive_mask = frequencies >= 0
    freq_pos = frequencies[positive_mask]
    psd_pos = psd[positive_mask]
    
    for band_name, (f_low, f_high) in bands.items():
        # Find frequencies in this band
        band_mask = (freq_pos >= f_low) & (freq_pos <= f_high)
        
        if np.any(band_mask):
            # Calculate total power in this band (integrate PSD)
            df = freq_pos[1] - freq_pos[0] if len(freq_pos) > 1 else 1
            band_power = np.sum(psd_pos[band_mask]) * df
            band_powers[band_name] = band_power
        else:
            band_powers[band_name] = 0.0
    
    return band_powers


def main():
    """
    Main function to execute Task 3: Frequency analysis of ECG signal.
    """
    print("Task 3: Frequency Analysis of ECG Signal")
    print("=" * 60)
    
    # Load and filter ECG data (reusing Task 1 functions)
    print("Loading and filtering ECG data...")
    signal_df, record = load_ecg_data()
    
    # Apply Butterworth filter
    cutoff_frequency = 40.0  # Hz
    b, a = design_butterworth_filter(cutoff_frequency, record.fs)
    filtered_signal = apply_filter(signal_df['MLII'].values, b, a)
    
    print(f"Data loaded: {len(filtered_signal)} samples at {record.fs} Hz")
    print(f"Signal duration: {len(filtered_signal) / record.fs:.2f} seconds")
    
    # Compute FFT
    print("\nComputing Fast Fourier Transform...")
    frequencies, fft_magnitude, fft_complex = compute_fft(filtered_signal, record.fs)
    
    # Compute Power Spectral Density
    print("Computing Power Spectral Density...")
    freq_psd, psd = compute_power_spectral_density(filtered_signal, record.fs)
    
    # Find dominant frequencies
    print("\nFinding dominant frequencies...")
    dominant_freqs = find_dominant_frequencies(frequencies, fft_magnitude, num_peaks=10)
    
    print("Top 10 Dominant Frequencies:")
    for i, (freq, mag) in enumerate(dominant_freqs, 1):
        print(f"{i:2d}. {freq:6.2f} Hz (magnitude: {mag:.2e})")
    
    # Analyze frequency bands
    print("\nAnalyzing ECG frequency bands...")
    band_powers = analyze_ecg_frequency_bands(frequencies, psd)
    
    print("Power in different frequency bands:")
    total_power = sum(band_powers.values())
    for band_name, power in band_powers.items():
        percentage = (power / total_power * 100) if total_power > 0 else 0
        print(f"{band_name:25s}: {power:.2e} V²/Hz ({percentage:5.1f}%)")
    
    # Plot frequency spectrum
    print("\nPlotting frequency spectrum...")
    plot_frequency_spectrum(
        frequencies, 
        fft_magnitude,
        title=f"FFT Magnitude Spectrum - Record {record.record_name}",
        freq_limit=50,
        log_scale=False
    )
    
    # Plot with logarithmic scale
    plot_frequency_spectrum(
        frequencies, 
        fft_magnitude,
        title=f"FFT Magnitude Spectrum (Log Scale) - Record {record.record_name}",
        freq_limit=50,
        log_scale=True
    )
    
    # Plot Power Spectral Density
    print("Plotting Power Spectral Density...")
    plot_power_spectral_density(
        freq_psd,
        psd,
        title=f"Power Spectral Density - Record {record.record_name}",
        freq_limit=50
    )
    
    return frequencies, fft_magnitude, psd, dominant_freqs, band_powers


if __name__ == "__main__":
    frequencies, fft_magnitude, psd, dominant_freqs, band_powers = main()
