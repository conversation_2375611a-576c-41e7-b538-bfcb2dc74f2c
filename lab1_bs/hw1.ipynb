from lab1_utils import load_ecg_data, butterworth_filter, apply_filter, plot_original_vs_filtered, detect_r_peaks, calculate_heart_rate, plot_ecg_with_rpeaks, analyze_heart_rate, compute_fft, compute_power_spectral_density, find_dominant_frequencies, plot_frequency_spectrum, plot_power_spectral_density, analyze_ecg_frequency_bands

signal_df, record = load_ecg_data()

print(f"Record: {record.record_name}")
print(f"Sampling frequency: {record.fs} Hz")
print(f"Signal channels: {record.sig_name}")

cutoff_frequency = 40.0

b, a = butterworth_filter(cutoff_frequency, record.fs, order=4, filter_type='low')

filtered_signal = apply_filter(signal_df['MLII'].values, b, a)

plot_original_vs_filtered(signal_df['MLII'].values, filtered_signal, record.fs, title=f"ECG Signal Filtering - Record {record.record_name}", signal_name="MLII")

peaks, peak_properties = detect_r_peaks(filtered_signal, record.fs)

instantaneous_hr, mean_hr, std_hr = calculate_heart_rate(peaks, record.fs)

plot_ecg_with_rpeaks(filtered_signal, peaks, record.fs, title=f"ECG with R-peaks - Record {record.record_name}", show_full_signal=False)

heart_stats = analyze_heart_rate(instantaneous_hr)

print(f"Average Heart Rate: {heart_stats['mean_hr']:.2f} ± {heart_stats['std_hr']:.2f} bpm")

frequencies, fft_magnitude, fft_complex = compute_fft(filtered_signal, record.fs)

freq_psd, psd = compute_power_spectral_density(filtered_signal, record.fs)

dominant_freqs = find_dominant_frequencies(frequencies, fft_magnitude, num_peaks=10)

print("Top 10 Dominant Frequencies:")
for i, (freq, mag) in enumerate(dominant_freqs, 1):
    print(f"{i:2d}. {freq:6.2f} Hz (magnitude: {mag:.2e})")

plot_frequency_spectrum(
    frequencies,
    fft_magnitude,
    title=f"FFT Magnitude Spectrum (Log Scale) - Record {record.record_name}",
    freq_limit=50,
    log_scale=True,
)