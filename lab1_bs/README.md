# ECG Signal Processing - Lab 1

This directory contains Python scripts for ECG signal processing tasks based on the MIT-BIH Arrhythmia Database.

## Tasks Overview

### Task 1: Butterworth Low-pass Filtering
- **File**: `task1_butterworth_filter.py`
- **Purpose**: Apply a low-pass Butterworth filter to remove high-frequency noise from ECG signals
- **Features**:
  - Load ECG data from MIT-BIH database
  - Design and apply 4th-order Butterworth filter (40 Hz cutoff)
  - Compare original vs filtered signals
  - Display signal statistics

### Task 2: R-peak Detection and Heart Rate Analysis
- **File**: `task2_rpeak_detection.py`
- **Purpose**: Detect R-peaks and calculate heart rate statistics
- **Features**:
  - Automatic R-peak detection using scipy.signal.find_peaks
  - Heart rate calculation from RR intervals
  - Heart rate variability analysis
  - Visualization of ECG with R-peak markers

### Task 3: Frequency Domain Analysis
- **File**: `task3_frequency_analysis.py`
- **Purpose**: Perform FFT analysis and identify dominant frequencies
- **Features**:
  - Fast Fourier Transform computation
  - Power Spectral Density calculation
  - Dominant frequency identification
  - ECG frequency band analysis (VLF, LF, HF, QRS, etc.)
  - Multiple visualization options

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure you have access to the MIT-BIH Arrhythmia Database through the `wfdb` package.

## Usage

### Run Individual Tasks

```bash
# Task 1: Butterworth Filtering
python task1_butterworth_filter.py

# Task 2: R-peak Detection
python task2_rpeak_detection.py

# Task 3: Frequency Analysis
python task3_frequency_analysis.py
```

### Run All Tasks Together

```bash
python run_all_tasks.py
```

This will execute all three tasks sequentially and provide a comprehensive analysis report.

## Output

Each script will:
1. Load ECG data from the MIT-BIH database (record 100 by default)
2. Process the signal according to the task requirements
3. Display analysis results in the console
4. Generate matplotlib plots for visualization
5. Return processed data for further analysis

## Key Features

### Signal Processing
- **Filtering**: 4th-order Butterworth low-pass filter (40 Hz cutoff)
- **Peak Detection**: Adaptive threshold-based R-peak detection
- **Frequency Analysis**: FFT with power spectral density computation

### Analysis Metrics
- **Heart Rate**: Mean, standard deviation, min/max values
- **Heart Rate Variability**: Coefficient of variation, range analysis
- **Frequency Bands**: Power distribution across ECG-relevant frequency bands
- **Dominant Frequencies**: Top frequency components in the signal

### Visualization
- Original vs filtered signal comparison
- ECG with R-peak markers
- Frequency spectrum (linear and logarithmic scales)
- Power spectral density plots

## Data Source

The scripts use the MIT-BIH Arrhythmia Database, specifically:
- **Record**: 100 (by default)
- **Duration**: First 2000 samples (~5.6 seconds at 360 Hz)
- **Lead**: MLII (Modified Lead II)

## Customization

You can modify the following parameters in each script:

### Task 1 (Filtering)
- `cutoff_frequency`: Filter cutoff frequency (default: 40 Hz)
- `filter_order`: Butterworth filter order (default: 4)

### Task 2 (R-peak Detection)
- `height_threshold`: Minimum peak height
- `distance_threshold`: Minimum distance between peaks
- `record_name`: ECG record to analyze

### Task 3 (Frequency Analysis)
- `freq_limit`: Maximum frequency for plots (default: 50 Hz)
- `num_peaks`: Number of dominant frequencies to identify
- `freq_range`: Frequency range for peak detection

## Dependencies

- `numpy`: Numerical computations
- `scipy`: Signal processing functions
- `matplotlib`: Plotting and visualization
- `pandas`: Data manipulation
- `wfdb`: ECG database access

## Notes

- The scripts are designed to work with the MIT-BIH Arrhythmia Database
- Default parameters are optimized for typical ECG signals
- All functions include comprehensive docstrings and type hints
- Error handling is implemented for robust execution
- Results can be saved for later analysis (see `run_all_tasks.py`)

## Example Output

When running all tasks, you'll see:
1. **Task 1**: Filtered vs original signal plots, noise reduction statistics
2. **Task 2**: R-peak detection results, heart rate analysis, HRV metrics
3. **Task 3**: Frequency spectrum plots, dominant frequencies, band power analysis

The comprehensive report includes all key metrics and can be saved for documentation purposes.
