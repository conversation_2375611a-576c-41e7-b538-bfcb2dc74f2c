"""
Main script to run all three ECG signal processing tasks.

This script executes:
1. Task 1: Butterworth filtering
2. Task 2: R-peak detection and heart rate calculation
3. Task 3: Frequency analysis using FFT

Usage:
    python run_all_tasks.py
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from task1_butterworth_filter import main as task1_main
from task2_rpeak_detection import main as task2_main
from task3_frequency_analysis import main as task3_main


def print_header(task_name: str, task_number: int):
    """Print a formatted header for each task."""
    print("\n" + "="*80)
    print(f"TASK {task_number}: {task_name}")
    print("="*80)


def print_summary():
    """Print a summary of all completed tasks."""
    print("\n" + "="*80)
    print("SUMMARY OF ALL TASKS")
    print("="*80)
    print("✓ Task 1: Applied Butterworth low-pass filter to remove high-frequency noise")
    print("✓ Task 2: Detected R-peaks and calculated heart rate statistics")
    print("✓ Task 3: Performed frequency analysis using FFT and identified dominant frequencies")
    print("\nAll ECG signal processing tasks completed successfully!")
    print("="*80)


def main():
    """
    Main function to execute all three tasks sequentially.
    """
    print("ECG SIGNAL PROCESSING - LAB 1")
    print("="*80)
    print("This script will execute three main tasks:")
    print("1. Butterworth Low-pass Filtering")
    print("2. R-peak Detection and Heart Rate Analysis")
    print("3. Frequency Domain Analysis using FFT")
    print("="*80)
    
    try:
        # Task 1: Butterworth Filtering
        print_header("BUTTERWORTH LOW-PASS FILTERING", 1)
        signal_df, record = task1_main()
        
        # Task 2: R-peak Detection
        print_header("R-PEAK DETECTION AND HEART RATE ANALYSIS", 2)
        peaks, instantaneous_hr, hrv_stats = task2_main()
        
        # Task 3: Frequency Analysis
        print_header("FREQUENCY DOMAIN ANALYSIS", 3)
        frequencies, fft_magnitude, psd, dominant_freqs, band_powers = task3_main()
        
        # Print summary
        print_summary()
        
        # Return all results
        results = {
            'signal_data': signal_df,
            'record': record,
            'r_peaks': peaks,
            'heart_rate': instantaneous_hr,
            'hrv_statistics': hrv_stats,
            'frequencies': frequencies,
            'fft_magnitude': fft_magnitude,
            'power_spectral_density': psd,
            'dominant_frequencies': dominant_freqs,
            'frequency_band_powers': band_powers
        }
        
        return results
        
    except Exception as e:
        print(f"\nError occurred during execution: {str(e)}")
        print("Please check that all required dependencies are installed:")
        print("- wfdb")
        print("- pandas")
        print("- numpy")
        print("- matplotlib")
        print("- scipy")
        raise


def create_comprehensive_report(results: dict):
    """
    Create a comprehensive report of all analysis results.
    
    Args:
        results: Dictionary containing all analysis results
    """
    print("\n" + "="*80)
    print("COMPREHENSIVE ECG ANALYSIS REPORT")
    print("="*80)
    
    record = results['record']
    hrv_stats = results['hrv_statistics']
    dominant_freqs = results['dominant_frequencies']
    band_powers = results['frequency_band_powers']
    
    print(f"\nRECORD INFORMATION:")
    print(f"Record ID: {record.record_name}")
    print(f"Database: MIT-BIH Arrhythmia Database")
    print(f"Sampling Frequency: {record.fs} Hz")
    print(f"Signal Channels: {', '.join(record.sig_name)}")
    print(f"Duration: {len(results['signal_data']) / record.fs:.2f} seconds")
    
    print(f"\nHEART RATE ANALYSIS:")
    print(f"Average Heart Rate: {hrv_stats['mean_hr']:.2f} ± {hrv_stats['std_hr']:.2f} bpm")
    print(f"Heart Rate Range: {hrv_stats['min_hr']:.2f} - {hrv_stats['max_hr']:.2f} bpm")
    print(f"Heart Rate Variability (CV): {hrv_stats['cv_hr']:.2f}%")
    print(f"Number of R-peaks detected: {len(results['r_peaks'])}")
    
    print(f"\nFREQUENCY ANALYSIS:")
    print("Top 5 Dominant Frequencies:")
    for i, (freq, mag) in enumerate(dominant_freqs[:5], 1):
        print(f"  {i}. {freq:6.2f} Hz")
    
    print(f"\nFREQUENCY BAND POWER DISTRIBUTION:")
    total_power = sum(band_powers.values())
    for band_name, power in band_powers.items():
        percentage = (power / total_power * 100) if total_power > 0 else 0
        print(f"  {band_name:25s}: {percentage:5.1f}%")
    
    print("="*80)


if __name__ == "__main__":
    # Execute all tasks
    results = main()
    
    # Create comprehensive report
    if results:
        create_comprehensive_report(results)
        
        # Optionally save results to file
        save_results = input("\nWould you like to save the analysis results to a file? (y/n): ")
        if save_results.lower() in ['y', 'yes']:
            import pickle
            import datetime
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ecg_analysis_results_{timestamp}.pkl"
            
            with open(filename, 'wb') as f:
                pickle.dump(results, f)
            
            print(f"Results saved to: {filename}")
            print("You can load these results later using:")
            print(f"import pickle")
            print(f"with open('{filename}', 'rb') as f:")
            print(f"    results = pickle.load(f)")
