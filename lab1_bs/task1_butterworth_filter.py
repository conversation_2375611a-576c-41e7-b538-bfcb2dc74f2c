"""
Task 1: Apply a low-pass Butterworth filter to the ECG data to remove high-frequency noise.

This module implements:
1. Import the necessary scipy library for signal processing
2. Design a low-pass Butterworth filter with an appropriate cutoff frequency
3. Apply the filter to the pre-loaded ECG data
4. Plot both the original and the filtered signals on the same graph for comparison
"""

import wfdb
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from typing import Tuple, Optional


def load_ecg_data(record_name: str = '100', database: str = 'mitdb', 
                  sampto: int = 2000) -> Tuple[pd.DataFrame, object]:
    """
    Load ECG data from the MIT-BIH Arrhythmia Database.
    
    Args:
        record_name: Name of the record to load
        database: Database name
        sampto: Number of samples to load
        
    Returns:
        Tuple of (signal_dataframe, record_object)
    """
    # Load ECG record and annotations
    record = wfdb.rdrecord(record_name, pn_dir=database, sampto=sampto)
    annotation = wfdb.rdann(record_name, 'atr', pn_dir=database, sampto=sampto)
    
    # Create DataFrame with signal data
    signal_df = pd.DataFrame(record.p_signal, columns=record.sig_name)
    
    return signal_df, record


def design_butterworth_filter(cutoff_freq: float, sampling_freq: float, 
                             order: int = 4, filter_type: str = 'low') -> Tuple[np.ndarray, np.ndarray]:
    """
    Design a Butterworth filter.
    
    Args:
        cutoff_freq: Cutoff frequency in Hz
        sampling_freq: Sampling frequency in Hz
        order: Filter order (default: 4)
        filter_type: Type of filter ('low', 'high', 'band', 'bandstop')
        
    Returns:
        Tuple of (numerator_coeffs, denominator_coeffs)
    """
    # Normalize the cutoff frequency (Nyquist frequency = sampling_freq/2)
    nyquist_freq = sampling_freq / 2
    normalized_cutoff = cutoff_freq / nyquist_freq
    
    # Design Butterworth filter
    b, a = signal.butter(order, normalized_cutoff, btype=filter_type, analog=False)
    
    return b, a


def apply_filter(data: np.ndarray, b: np.ndarray, a: np.ndarray) -> np.ndarray:
    """
    Apply the designed filter to the signal using zero-phase filtering.
    
    Args:
        data: Input signal data
        b: Numerator coefficients of the filter
        a: Denominator coefficients of the filter
        
    Returns:
        Filtered signal
    """
    # Use filtfilt for zero-phase filtering (no phase distortion)
    filtered_data = signal.filtfilt(b, a, data)
    
    return filtered_data


def plot_original_vs_filtered(original_signal: np.ndarray, filtered_signal: np.ndarray,
                             sampling_freq: float, title: str = "ECG Signal Comparison",
                             signal_name: str = "MLII") -> None:
    """
    Plot original and filtered signals for comparison.
    
    Args:
        original_signal: Original ECG signal
        filtered_signal: Filtered ECG signal
        sampling_freq: Sampling frequency
        title: Plot title
        signal_name: Name of the signal channel
    """
    # Create time axis
    time_axis = np.arange(len(original_signal)) / sampling_freq
    
    plt.figure(figsize=(12, 6))
    
    # Plot original signal
    plt.plot(time_axis, original_signal, label=f"Original {signal_name}", 
             color='blue', alpha=0.7, linewidth=1)
    
    # Plot filtered signal
    plt.plot(time_axis, filtered_signal, label=f"Filtered {signal_name}", 
             color='red', alpha=0.8, linewidth=1.5)
    
    plt.xlabel('Time (seconds)')
    plt.ylabel('Amplitude (mV)')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def main():
    """
    Main function to execute Task 1: Butterworth filtering of ECG data.
    """
    print("Task 1: Applying Butterworth Low-pass Filter to ECG Data")
    print("=" * 60)
    
    # Load ECG data
    print("Loading ECG data from MIT-BIH database...")
    signal_df, record = load_ecg_data()
    
    # Display basic information about the data
    print(f"Record: {record.record_name}")
    print(f"Sampling frequency: {record.fs} Hz")
    print(f"Signal channels: {record.sig_name}")
    print(f"Signal duration: {len(signal_df) / record.fs:.2f} seconds")
    print(f"Number of samples: {len(signal_df)}")
    
    # Choose appropriate cutoff frequency for ECG (typically 40-50 Hz for diagnostic ECG)
    cutoff_frequency = 40.0  # Hz
    print(f"\nDesigning Butterworth low-pass filter with cutoff frequency: {cutoff_frequency} Hz")
    
    # Design the Butterworth filter
    b, a = design_butterworth_filter(cutoff_frequency, record.fs, order=4, filter_type='low')
    
    # Apply filter to the MLII lead (Modified Lead II)
    ecg_signal = signal_df['MLII'].values
    filtered_signal = apply_filter(ecg_signal, b, a)
    
    print("Filter applied successfully!")
    
    # Add filtered signal to DataFrame
    signal_df['MLII_filtered'] = filtered_signal
    
    # Plot comparison
    print("\nPlotting original vs filtered signals...")
    plot_original_vs_filtered(
        ecg_signal, 
        filtered_signal, 
        record.fs,
        title=f"ECG Signal Filtering - Record {record.record_name}",
        signal_name="MLII"
    )
    
    # Display some statistics
    print("\nSignal Statistics:")
    print(f"Original signal - Mean: {np.mean(ecg_signal):.3f}, Std: {np.std(ecg_signal):.3f}")
    print(f"Filtered signal - Mean: {np.mean(filtered_signal):.3f}, Std: {np.std(filtered_signal):.3f}")
    
    return signal_df, record


if __name__ == "__main__":
    signal_df, record = main()
