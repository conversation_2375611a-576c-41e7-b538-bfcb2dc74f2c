"""
Task 2: Detect R-peaks from the filtered ECG signal and calculate the average heart rate.

This module implements:
1. Import necessary libraries for peak detection (find_peaks)
2. Use signal detection methods to identify R-peaks in the ECG signal
3. Calculate the intervals between successive R-peaks to estimate the heart rate
4. Plot the filtered ECG signal with markers indicating the position of R-peaks
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
from typing import Tuple, List, Optional
import pandas as pd
from task1_butterworth_filter import load_ecg_data, design_butterworth_filter, apply_filter


def detect_r_peaks(filtered_signal: np.ndarray, sampling_freq: float, 
                   height_threshold: Optional[float] = None,
                   distance_threshold: Optional[float] = None) -> Tuple[np.ndarray, dict]:
    """
    Detect R-peaks in the filtered ECG signal.
    
    Args:
        filtered_signal: Filtered ECG signal
        sampling_freq: Sampling frequency in Hz
        height_threshold: Minimum height for peak detection (if None, auto-calculated)
        distance_threshold: Minimum distance between peaks in seconds (if None, auto-calculated)
        
    Returns:
        Tuple of (peak_indices, peak_properties)
    """
    # Auto-calculate thresholds if not provided
    if height_threshold is None:
        # Use a percentage of the signal's standard deviation above the mean
        signal_mean = np.mean(filtered_signal)
        signal_std = np.std(filtered_signal)
        height_threshold = signal_mean + 0.5 * signal_std
    
    if distance_threshold is None:
        # Minimum distance between R-peaks (assume max heart rate of 200 bpm)
        # This corresponds to minimum RR interval of 0.3 seconds
        distance_threshold = 0.3  # seconds
    
    # Convert distance threshold to samples
    min_distance_samples = int(distance_threshold * sampling_freq)
    
    # Find peaks using scipy's find_peaks function
    peaks, properties = find_peaks(
        filtered_signal,
        height=height_threshold,
        distance=min_distance_samples,
        prominence=None  # Let the algorithm determine prominence automatically
    )
    
    return peaks, properties


def calculate_heart_rate(peak_indices: np.ndarray, sampling_freq: float) -> Tuple[np.ndarray, float, float]:
    """
    Calculate heart rate from R-peak intervals.
    
    Args:
        peak_indices: Array of R-peak sample indices
        sampling_freq: Sampling frequency in Hz
        
    Returns:
        Tuple of (instantaneous_heart_rates, mean_heart_rate, std_heart_rate)
    """
    if len(peak_indices) < 2:
        raise ValueError("Need at least 2 R-peaks to calculate heart rate")
    
    # Calculate RR intervals in seconds
    rr_intervals = np.diff(peak_indices) / sampling_freq
    
    # Calculate instantaneous heart rates (beats per minute)
    instantaneous_hr = 60.0 / rr_intervals
    
    # Calculate statistics
    mean_hr = np.mean(instantaneous_hr)
    std_hr = np.std(instantaneous_hr)
    
    return instantaneous_hr, mean_hr, std_hr


def plot_ecg_with_rpeaks(filtered_signal: np.ndarray, peak_indices: np.ndarray,
                        sampling_freq: float, title: str = "ECG with R-peaks",
                        show_full_signal: bool = True) -> None:
    """
    Plot the filtered ECG signal with R-peak markers.
    
    Args:
        filtered_signal: Filtered ECG signal
        peak_indices: Array of R-peak sample indices
        sampling_freq: Sampling frequency
        title: Plot title
        show_full_signal: If True, show entire signal; if False, show first 10 seconds
    """
    # Create time axis
    time_axis = np.arange(len(filtered_signal)) / sampling_freq
    
    # Limit display to first 10 seconds if requested
    if not show_full_signal:
        max_samples = int(10 * sampling_freq)
        time_axis = time_axis[:max_samples]
        filtered_signal = filtered_signal[:max_samples]
        peak_indices = peak_indices[peak_indices < max_samples]
    
    plt.figure(figsize=(14, 6))
    
    # Plot filtered ECG signal
    plt.plot(time_axis, filtered_signal, label="Filtered ECG", 
             color='blue', linewidth=1, alpha=0.8)
    
    # Plot R-peaks
    if len(peak_indices) > 0:
        peak_times = peak_indices / sampling_freq
        peak_amplitudes = filtered_signal[peak_indices]
        plt.plot(peak_times, peak_amplitudes, 'ro', 
                label=f"R-peaks ({len(peak_indices)} detected)", 
                markersize=8, markerfacecolor='red', markeredgecolor='darkred')
    
    plt.xlabel('Time (seconds)')
    plt.ylabel('Amplitude (mV)')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def analyze_heart_rate_variability(instantaneous_hr: np.ndarray) -> dict:
    """
    Perform basic heart rate variability analysis.
    
    Args:
        instantaneous_hr: Array of instantaneous heart rates
        
    Returns:
        Dictionary with HRV statistics
    """
    hrv_stats = {
        'mean_hr': np.mean(instantaneous_hr),
        'std_hr': np.std(instantaneous_hr),
        'min_hr': np.min(instantaneous_hr),
        'max_hr': np.max(instantaneous_hr),
        'range_hr': np.max(instantaneous_hr) - np.min(instantaneous_hr),
        'cv_hr': np.std(instantaneous_hr) / np.mean(instantaneous_hr) * 100  # Coefficient of variation
    }
    
    return hrv_stats


def main():
    """
    Main function to execute Task 2: R-peak detection and heart rate calculation.
    """
    print("Task 2: R-peak Detection and Heart Rate Calculation")
    print("=" * 60)
    
    # Load and filter ECG data (reusing Task 1 functions)
    print("Loading and filtering ECG data...")
    signal_df, record = load_ecg_data()
    
    # Apply Butterworth filter
    cutoff_frequency = 40.0  # Hz
    b, a = design_butterworth_filter(cutoff_frequency, record.fs)
    filtered_signal = apply_filter(signal_df['MLII'].values, b, a)
    
    print(f"Data loaded: {len(filtered_signal)} samples at {record.fs} Hz")
    
    # Detect R-peaks
    print("\nDetecting R-peaks...")
    peaks, peak_properties = detect_r_peaks(filtered_signal, record.fs)
    
    print(f"Detected {len(peaks)} R-peaks")
    
    if len(peaks) < 2:
        print("Error: Not enough R-peaks detected for heart rate calculation")
        return
    
    # Calculate heart rate
    print("\nCalculating heart rate...")
    instantaneous_hr, mean_hr, std_hr = calculate_heart_rate(peaks, record.fs)
    
    # Display results
    print(f"\nHeart Rate Analysis:")
    print(f"Average Heart Rate: {mean_hr:.2f} ± {std_hr:.2f} bpm")
    print(f"Number of beats: {len(peaks)}")
    print(f"Recording duration: {len(filtered_signal) / record.fs:.2f} seconds")
    
    # Heart rate variability analysis
    hrv_stats = analyze_heart_rate_variability(instantaneous_hr)
    print(f"\nHeart Rate Variability:")
    print(f"Min HR: {hrv_stats['min_hr']:.2f} bpm")
    print(f"Max HR: {hrv_stats['max_hr']:.2f} bpm")
    print(f"HR Range: {hrv_stats['range_hr']:.2f} bpm")
    print(f"Coefficient of Variation: {hrv_stats['cv_hr']:.2f}%")
    
    # Plot ECG with R-peaks (first 10 seconds for clarity)
    print("\nPlotting ECG signal with detected R-peaks...")
    plot_ecg_with_rpeaks(
        filtered_signal, 
        peaks, 
        record.fs,
        title=f"ECG with R-peaks - Record {record.record_name}",
        show_full_signal=False  # Show first 10 seconds for better visualization
    )
    
    # Plot full signal
    plot_ecg_with_rpeaks(
        filtered_signal, 
        peaks, 
        record.fs,
        title=f"Complete ECG with R-peaks - Record {record.record_name}",
        show_full_signal=True
    )
    
    return peaks, instantaneous_hr, hrv_stats


if __name__ == "__main__":
    peaks, instantaneous_hr, hrv_stats = main()
