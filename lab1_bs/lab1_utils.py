import wfdb
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from typing import <PERSON>ple, Optional, List
from scipy.signal import find_peaks
from scipy.fft import fft, fftfreq, fftshift


def load_ecg_data(
    record_name: str = "100", database: str = "mitdb", sampto: int = 1000
) -> <PERSON><PERSON>[pd.DataFrame, object]:
    """
    Load ECG data from the MIT-BIH Arrhythmia Database.
    """
    # Load ECG record and annotations
    record = wfdb.rdrecord(record_name, pn_dir=database, sampto=sampto)
    annotation = wfdb.rdann(record_name, "atr", pn_dir=database, sampto=sampto)

    # Create DataFrame with signal data
    signal_df = pd.DataFrame(record.p_signal, columns=record.sig_name)

    return signal_df, record


def butterworth_filter(
    cutoff_freq: float, sampling_freq: float, order: int = 4, filter_type: str = "low"
) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
    """
    Implementation of the Butterworth filter.
    """
    # Normalize the cutoff frequency (Nyquist frequency = sampling_freq/2)
    nyquist_freq = sampling_freq / 2
    normalized_cutoff = cutoff_freq / nyquist_freq

    # Butterworth filter
    b, a = signal.butter(order, normalized_cutoff, btype=filter_type, analog=False)

    return b, a


def apply_filter(data: np.ndarray, b: np.ndarray, a: np.ndarray) -> np.ndarray:
    """
    Apply the designed filter to the signal using zero-phase filtering.
    """
    filtered_data = signal.filtfilt(b, a, data)

    return filtered_data


def plot_original_vs_filtered(
    original_signal: np.ndarray,
    filtered_signal: np.ndarray,
    sampling_freq: float,
    title: str = "ECG Signal Comparison",
    signal_name: str = "MLII",
) -> None:
    """
    Plot original and filtered signals for comparison.
    """
    time_axis = np.arange(len(original_signal)) / sampling_freq

    plt.figure(figsize=(12, 6))

    # Plot original signal
    plt.plot(
        time_axis,
        original_signal,
        label=f"Original {signal_name}",
        color="blue",
        alpha=0.7,
        linewidth=1,
    )

    # Plot filtered signal
    plt.plot(
        time_axis,
        filtered_signal,
        label=f"Filtered {signal_name}",
        color="red",
        alpha=0.8,
        linewidth=1.5,
    )

    plt.xlabel("Time (seconds)")
    plt.ylabel("Amplitude (mV)")
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def detect_r_peaks(
    filtered_signal: np.ndarray,
    sampling_freq: float,
    height_threshold: Optional[float] = None,
    distance_threshold: Optional[float] = None,
) -> Tuple[np.ndarray, dict]:
    """
    Detect R-peaks in the filtered ECG signal.
    """
    if height_threshold is None:
        # Use a percentage of the standard deviation above the mean
        signal_mean = np.mean(filtered_signal)
        signal_std = np.std(filtered_signal)
        height_threshold = signal_mean + 0.5 * signal_std

    if distance_threshold is None:
        # Minimum distance between R-peaks (assume max heart rate of 200 bpm)
        distance_threshold = 0.3

    min_distance_samples = int(distance_threshold * sampling_freq)

    peaks, properties = find_peaks(
        filtered_signal,
        height=height_threshold,
        distance=min_distance_samples,
        prominence=None,
    )

    return peaks, properties


def calculate_heart_rate(
    peak_indices: np.ndarray, sampling_freq: float
) -> Tuple[np.ndarray, float, float]:
    """
    Calculate heart rate from R-peak intervals.
    """
    if len(peak_indices) < 2:
        raise ValueError("Need at least 2 R-peaks to calculate heart rate")

    # Calculate RR intervals in seconds
    rr_intervals = np.diff(peak_indices) / sampling_freq

    # Calculate heart rates
    hr = 60.0 / rr_intervals

    # Calculate statistics
    mean_hr = np.mean(hr)
    std_hr = np.std(hr)

    return hr, mean_hr, std_hr


def plot_ecg_with_rpeaks(
    filtered_signal: np.ndarray,
    peak_indices: np.ndarray,
    sampling_freq: float,
    title: str = "ECG with R-peaks",
) -> None:
    """
    Plot the filtered ECG signal with R-peak markers.
    """
    time_axis = np.arange(len(filtered_signal)) / sampling_freq

    plt.figure(figsize=(14, 6))

    # Plot filtered ECG signal
    plt.plot(
        time_axis,
        filtered_signal,
        label="Filtered ECG",
        color="blue",
        linewidth=1,
        alpha=0.8,
    )

    # Plot R-peaks
    if len(peak_indices) > 0:
        peak_times = peak_indices / sampling_freq
        peak_amplitudes = filtered_signal[peak_indices]
        plt.plot(
            peak_times,
            peak_amplitudes,
            "ro",
            label=f"R-peaks ({len(peak_indices)} detected)",
            markersize=8,
            markerfacecolor="red",
            markeredgecolor="darkred",
        )

    plt.xlabel("Time (seconds)")
    plt.ylabel("Amplitude (mV)")
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def analyze_heart_rate(hr: np.ndarray) -> dict:
    """
    Perform basic heart rate variability analysis.
    """
    heart_stats = {
        "mean_hr": np.mean(hr),
        "std_hr": np.std(hr),
        "min_hr": np.min(hr),
        "max_hr": np.max(hr),
        "range_hr": np.max(hr) - np.min(hr),
    }

    return heart_stats


def compute_fft(
    signal: np.ndarray, sampling_freq: float
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Compute the Fast Fourier Transform of the signal.
    """
    # Number of samples
    N = len(signal)

    # Compute FFT
    fft_complex = fft(signal)

    # Compute frequency bins
    frequencies = fftfreq(N, 1 / sampling_freq)

    # Compute magnitude spectrum
    fft_magnitude = np.abs(fft_complex)

    return frequencies, fft_magnitude, fft_complex


def compute_power_spectral_density(
    signal: np.ndarray, sampling_freq: float
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute the Power Spectral Density (PSD) of the signal.
    """
    frequencies, fft_magnitude, _ = compute_fft(signal, sampling_freq)

    # magnitude squared and normalized
    N = len(signal)
    psd = (fft_magnitude**2) / (N * sampling_freq)

    return frequencies, psd


def find_dominant_frequencies(
    frequencies: np.ndarray,
    magnitude: np.ndarray,
    num_peaks: int = 5,
    freq_range: Tuple[float, float] = (0.5, 50),
) -> List[Tuple[float, float]]:
    """
    Find the dominant frequencies in the spectrum.
    """
    # Consider only positive frequencies
    positive_freq_mask = (frequencies >= freq_range[0]) & (frequencies <= freq_range[1])
    freq_subset = frequencies[positive_freq_mask]
    mag_subset = magnitude[positive_freq_mask]

    # Find peaks with some minimum prominence
    peak_indices, _ = find_peaks(mag_subset, prominence=np.max(mag_subset) * 0.1)

    if len(peak_indices) == 0:
        # If no peaks found just take the maximum
        max_idx = np.argmax(mag_subset)
        return [(freq_subset[max_idx], mag_subset[max_idx])]

    # Sort peaks by magnitude
    peak_magnitudes = mag_subset[peak_indices]
    sorted_indices = np.argsort(peak_magnitudes)[::-1]

    dominant_freqs = []
    for i in range(min(num_peaks, len(sorted_indices))):
        idx = peak_indices[sorted_indices[i]]
        dominant_freqs.append((freq_subset[idx], mag_subset[idx]))

    return dominant_freqs


def plot_frequency_spectrum(
    frequencies: np.ndarray,
    magnitude: np.ndarray,
    title: str = "Frequency Spectrum",
    freq_limit: float = 100,
    log_scale: bool = False,
) -> None:
    """
    Plot the frequency spectrum.
    """
    # Consider only positive frequencies up to freq_limit
    positive_mask = (frequencies >= 0) & (frequencies <= freq_limit)
    freq_plot = frequencies[positive_mask]
    mag_plot = magnitude[positive_mask]

    plt.figure(figsize=(12, 6))

    if log_scale:
        plt.semilogy(freq_plot, mag_plot, "b-", linewidth=1)
        plt.ylabel("Magnitude (log scale)")
    else:
        plt.plot(freq_plot, mag_plot, "b-", linewidth=1)
        plt.ylabel("Magnitude")

    plt.xlabel("Frequency (Hz)")
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
