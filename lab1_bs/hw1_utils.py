import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import find_peaks
from typing import Tuple, List, Dict, Optional
import pandas as pd
from lab1_utils import apply_filter, butterworth_filter


def highpass_filter(
    cutoff_freq: float, sampling_freq: float, order: int = 4
) -> Tuple[np.ndarray, np.ndarray]:
    """
    High-pass Butterworth filter.
    """
    return butterworth_filter(cutoff_freq, sampling_freq, order, "high")


def bandpass_filter(
    low_freq: float, high_freq: float, sampling_freq: float, order: int = 4
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Band-pass Butterworth filter.
    """
    nyquist_freq = sampling_freq / 2
    low_normalized = low_freq / nyquist_freq
    high_normalized = high_freq / nyquist_freq

    b, a = signal.butter(
        order, [low_normalized, high_normalized], btype="band", analog=False
    )
    return b, a


def notch_filter(
    notch_freq: float, sampling_freq: float, quality_factor: float = 30.0
) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
    """
    Notch filter to remove specific frequency (e.g., 50/60 Hz power line interference).
    """
    nyquist_freq = sampling_freq / 2
    normalized_freq = notch_freq / nyquist_freq

    b, a = signal.iirnotch(normalized_freq, quality_factor)
    return b, a


def compare_filters_on_ecg(
    ecg_signal: np.ndarray, sampling_freq: float, filter_configs: List[Dict]
) -> Dict[str, np.ndarray]:
    """
    Apply different filters to the same ECG signal and return results for comparison.
    """
    filtered_signals = {"original": ecg_signal}

    for config in filter_configs:
        filter_name = config["name"]
        filter_type = config["type"]

        if filter_type == "lowpass":
            b, a = butterworth_filter(
                config["cutoff"], sampling_freq, config.get("order", 4), "low"
            )
        elif filter_type == "highpass":
            b, a = highpass_filter(
                config["cutoff"], sampling_freq, config.get("order", 4)
            )
        elif filter_type == "bandpass":
            b, a = bandpass_filter(
                config["low_freq"],
                config["high_freq"],
                sampling_freq,
                config.get("order", 4),
            )
        elif filter_type == "notch":
            b, a = notch_filter(
                config["notch_freq"], sampling_freq, config.get("quality_factor", 30.0)
            )

        filtered_signals[filter_name] = apply_filter(ecg_signal, b, a)

    return filtered_signals


def plot_filter_comparison(
    filtered_signals: Dict[str, np.ndarray],
    sampling_freq: float,
    time_range: Optional[Tuple[float, float]] = None,
    title: str = "Filter Comparison on ECG Signal",
) -> None:
    """
    Plot multiple filtered signals for comparison.
    """
    signal_length = len(next(iter(filtered_signals.values())))
    time_axis = np.arange(signal_length) / sampling_freq

    if time_range:
        start_idx = int(time_range[0] * sampling_freq)
        end_idx = int(time_range[1] * sampling_freq)
        time_axis = time_axis[start_idx:end_idx]
    else:
        start_idx, end_idx = 0, signal_length

    plt.figure(figsize=(12, 8))

    colors = ["blue", "red", "green", "orange", "purple", "brown", "pink"]

    for i, (name, signal_data) in enumerate(filtered_signals.items()):
        signal_segment = signal_data[start_idx:end_idx]
        color = colors[i % len(colors)]
        alpha = 0.8 if name == "original" else 0.7
        linewidth = 1.5 if name == "original" else 1.0

        plt.plot(
            time_axis,
            signal_segment,
            label=name,
            color=color,
            alpha=alpha,
            linewidth=linewidth,
        )

    plt.xlabel("Time (seconds)")
    plt.ylabel("Amplitude (mV)")
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def detect_p_waves(
    ecg_signal: np.ndarray,
    r_peaks: np.ndarray,
    sampling_freq: float,
    search_window: float = 0.2,
) -> np.ndarray:
    """
    Detect P waves in ECG signal relative to R peaks.
    """
    p_waves = []
    search_samples = int(search_window * sampling_freq)

    for r_peak in r_peaks:
        # Define search window before R peak
        start_idx = max(0, r_peak - search_samples)
        end_idx = r_peak

        if start_idx >= end_idx:
            continue

        # Search for local maxima in the search window
        search_segment = ecg_signal[start_idx:end_idx]

        # Find peaks with lower threshold than R peaks
        peaks, _ = find_peaks(
            search_segment,
            height=np.mean(search_segment) + 0.1 * np.std(search_segment),
        )

        if len(peaks) > 0:
            # Take the peak closest to R peak
            p_wave_relative = peaks[-1]
            p_wave_absolute = start_idx + p_wave_relative
            p_waves.append(p_wave_absolute)

    return np.array(p_waves)


def detect_t_waves(
    ecg_signal: np.ndarray,
    r_peaks: np.ndarray,
    sampling_freq: float,
    search_window: float = 0.4,
) -> np.ndarray:
    """
    Detect T waves in ECG signal relative to R peaks.
    """
    t_waves = []
    search_samples = int(search_window * sampling_freq)

    for r_peak in r_peaks:
        # Define search window after R peak
        start_idx = r_peak
        end_idx = min(len(ecg_signal), r_peak + search_samples)

        if start_idx >= end_idx:
            continue

        # Search for local maxima in the search window
        search_segment = ecg_signal[start_idx:end_idx]

        # Find peaks with lower threshold than R peaks
        peaks, _ = find_peaks(
            search_segment,
            height=np.mean(search_segment) + 0.05 * np.std(search_segment),
        )

        if len(peaks) > 0:
            # Take the highest peak in the search window
            peak_heights = search_segment[peaks]
            max_peak_idx = peaks[np.argmax(peak_heights)]
            t_wave_absolute = start_idx + max_peak_idx
            t_waves.append(t_wave_absolute)

    return np.array(t_waves)


def calculate_ecg_intervals(
    r_peaks: np.ndarray, p_waves: np.ndarray, t_waves: np.ndarray, sampling_freq: float
) -> Dict[str, np.ndarray]:
    """
    Calculate ECG intervals (PR, QT, RR).
    """
    intervals = {}

    # RR intervals
    if len(r_peaks) > 1:
        rr_intervals = np.diff(r_peaks) / sampling_freq
        intervals["RR"] = rr_intervals

    # PR intervals
    pr_intervals = []
    for r_peak in r_peaks:
        # Find the closest P wave before this R peak
        p_before_r = p_waves[p_waves < r_peak]
        if len(p_before_r) > 0:
            closest_p = p_before_r[-1]  # Last P wave before R
            pr_interval = (r_peak - closest_p) / sampling_freq
            pr_intervals.append(pr_interval)
    intervals["PR"] = np.array(pr_intervals)

    # QT intervals
    qt_intervals = []
    for r_peak in r_peaks:
        # Find the closest T wave after this R peak
        t_after_r = t_waves[t_waves > r_peak]
        if len(t_after_r) > 0:
            closest_t = t_after_r[0]  # First T wave after R
            qt_interval = (closest_t - r_peak) / sampling_freq
            qt_intervals.append(qt_interval)
    intervals["QT"] = np.array(qt_intervals)

    return intervals


def plot_ecg_with_all_waves(
    ecg_signal: np.ndarray,
    r_peaks: np.ndarray,
    p_waves: np.ndarray,
    t_waves: np.ndarray,
    sampling_freq: float,
    title: str = "ECG with P, R, and T Wave Detection",
) -> None:
    """
    Plot ECG signal with P, R, and T wave markers.
    """
    time_axis = np.arange(len(ecg_signal)) / sampling_freq

    plt.figure(figsize=(15, 8))

    # Plot ECG signal
    plt.plot(time_axis, ecg_signal, "b-", label="ECG Signal", linewidth=1, alpha=0.8)

    # Plot R peaks
    if len(r_peaks) > 0:
        r_times = r_peaks / sampling_freq
        r_amplitudes = ecg_signal[r_peaks]
        plt.plot(
            r_times,
            r_amplitudes,
            "ro",
            label=f"R peaks ({len(r_peaks)})",
            markersize=8,
            markerfacecolor="red",
            markeredgecolor="darkred",
        )

    # Plot P waves
    if len(p_waves) > 0:
        p_times = p_waves / sampling_freq
        p_amplitudes = ecg_signal[p_waves]
        plt.plot(
            p_times,
            p_amplitudes,
            "go",
            label=f"P waves ({len(p_waves)})",
            markersize=6,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
        )

    # Plot T waves
    if len(t_waves) > 0:
        t_times = t_waves / sampling_freq
        t_amplitudes = ecg_signal[t_waves]
        plt.plot(
            t_times,
            t_amplitudes,
            "mo",
            label=f"T waves ({len(t_waves)})",
            markersize=6,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
        )

    plt.xlabel("Time (seconds)")
    plt.ylabel("Amplitude (mV)")
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def analyze_ecg_intervals(
    intervals: Dict[str, np.ndarray],
) -> Dict[str, Dict[str, float]]:
    """
    Analyze ECG intervals.
    """
    stats = {}

    for interval_type, interval_values in intervals.items():
        if len(interval_values) > 0:
            stats[interval_type] = {
                "mean": np.mean(interval_values),
                "std": np.std(interval_values),
            }
        else:
            stats[interval_type] = {"mean": 0, "std": 0}

    return stats


def create_filter_config() -> List[Dict]:
    """
    Create filter configurations.
    """
    filter_configs = [
        {"name": "Low-pass (40 Hz)", "type": "lowpass", "cutoff": 40, "order": 4},
        {"name": "High-pass (0.5 Hz)", "type": "highpass", "cutoff": 0.5, "order": 4},
        {
            "name": "Band-pass (0.5-40 Hz)",
            "type": "bandpass",
            "low_freq": 0.5,
            "high_freq": 40,
            "order": 4,
        },
        {
            "name": "Notch (50 Hz)",
            "type": "notch",
            "notch_freq": 50,
            "quality_factor": 30,
        },
    ]
    return filter_configs


def ecg_analysis(ecg_signal: np.ndarray, sampling_freq: float) -> Dict:
    """
    Perform ECG analysis including filtering and wave detection.
    """
    from lab1_utils import detect_r_peaks

    b, a = bandpass_filter(0.5, 40, sampling_freq, 4)

    filtered_signal = apply_filter(ecg_signal, b, a)

    # Detect waves
    r_peaks, _ = detect_r_peaks(filtered_signal, sampling_freq)
    p_waves = detect_p_waves(filtered_signal, r_peaks, sampling_freq)
    t_waves = detect_t_waves(filtered_signal, r_peaks, sampling_freq)

    # Calculate intervals
    intervals = calculate_ecg_intervals(r_peaks, p_waves, t_waves, sampling_freq)

    # Analyze intervals
    interval_stats = analyze_ecg_intervals(intervals)

    return {
        "filtered_signal": filtered_signal,
        "r_peaks": r_peaks,
        "p_waves": p_waves,
        "t_waves": t_waves,
        "intervals": intervals,
        "interval_stats": interval_stats,
    }
